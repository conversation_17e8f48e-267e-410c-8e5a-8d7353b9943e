@use "../abstracts" as *;

//common table style -----------
.table-header {
  margin-bottom: 15px;
  h4 {
    color: $white;
    font-size: $text-lg;
    // font-family: "Manrope", sans-serif;
    font-weight: 600;
    span {
      color: $secondary;
      font-weight: 600;
    }
  }
}
.table-responsive {
  font-size: $text-sm;
  border-radius: 12px;
  &.min-data{
    min-height: 300px;
  }
  .table {
    border-radius: 12px;
    overflow: hidden;
    th,
    td {
      color: $dark;
      padding: 10px 15px;
      white-space: nowrap;
    }
    thead {
      position: sticky;
      top: 0;
      width: 100%;
      backdrop-filter: blur(10px);
      z-index: 100;
      tr {
        th {
          font-size: $text-md;
          background-color: $primary;
          color: $white;
        }
      }
    }
    tbody {
      tr {
        td {
          border-color: rgba($white, 0.4);
          vertical-align: middle;
          .clear-btn {
            svg {
              width: 20px;
              height: 20px;
            }
          }
        }
        &:last-child {
          td {
            border: none;
          }
        }
        &:nth-child(odd) {
          td {
            background-color: rgba($primary, 0.06);
          }
        }
      }
    }
    a {
      font-weight: $medium;
      &.dark {
        color: $dark;
      }
      &.primary {
        color: $primary;
      }
      &.underline {
        text-decoration: underline !important;
      }
    }
    .multi-user-list {
      @extend %listSpacing;
      display: flex;
      align-items: center;
      li {
        img {
          width: 25px;
          height: 25px;
          border-radius: 100%;
          object-fit: cover;
          border: 1px solid $white;
          overflow: hidden;
        }
        &:not(:first-child) {
          margin-left: -8px;
        }
      }
    }
    .multi-action-links {
      display: flex;
      gap: 15px;
      align-items: center;
      justify-content: flex-start;
    }
  }
}
