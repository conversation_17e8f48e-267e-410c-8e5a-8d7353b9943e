/* eslint-disable react-hooks/exhaustive-deps */
"use client";

import React, { useCallback, useEffect, useState } from "react";
import dayjs from "dayjs";
// import { useForm } from "react-hook-form";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";
// import InfiniteScroll from "react-infinite-scroll-component";

import Button from "@/components/formElements/Button";
// import InputWrapper from "@/components/formElements/InputWrapper";
// import Textbox from "@/components/formElements/Textbox";
// import SearchIcon from "@/components/svgComponents/SearchIcon";
import ThreeDotsIcon from "@/components/svgComponents/ThreeDotsIcon";
import CandidateApproveRejectModal from "@/components/commonModals/CandidateApproveRejectModal";
import BackArrowIcon from "@/components/svgComponents/BackArrowIcon";
import ArchiveCandidateModal from "@/components/commonModals/ArchiveCandidateModal"; //

// Services
import {
  fetchCandidatesApplications,
  fetchTopCandidatesApplications,
  promoteDemoteCandidate,
} from "@/services/CandidatesServices/candidatesApplicationServices";
import { archiveActiveApplication } from "@/services/CandidatesServices/candidatesApplicationStatusUpdateService";

import { AuthState } from "@/redux/slices/authSlice";
import { DEFAULT_LIMIT, PERMISSION } from "@/constants/commonConstants";
import { APPLICATION_STATUS } from "@/constants/jobRequirementConstant";

import style from "@/styles/commonPage.module.scss";
import { useTranslations } from "use-intl";
import { CandidateApplication, PromoteDemotePayload, topCandidateApplication } from "@/interfaces/candidatesInterface";
import "react-loading-skeleton/dist/skeleton.css";
import InfiniteScroll from "react-infinite-scroll-component";
import { APPLICATION_UPDATE_STATUS } from "@/constants/screenResumeConstant";
import { toastMessageError, toTitleCase } from "@/utils/helper";
import TableSkeleton from "../skeletons/TableSkeleton";
import ROUTES from "@/constants/routes";
import { debounce } from "lodash";
import FullPageLoader from "@/components/commonComponent/FullPageLoader";
// import InputWrapper from "@/components/formElements/InputWrapper";
// import Textbox from "@/components/formElements/Textbox";
// import SearchIcon from "@/components/svgComponents/SearchIcon";
// import { useForm } from "react-hook-form";

const CandidatesList = ({
  params,
  searchParams,
}: {
  params: Promise<{ jobId: string }>;
  searchParams: Promise<{ title: string; jobUniqueId: string }>;
}) => {
  // const { control } = useForm();
  const userData = useSelector((state: { auth: AuthState }) => state.auth.authData);
  const userPermissions = useSelector((state: { auth: AuthState }) => state.auth.permissions || []) as unknown as string[];
  const hasArchiveRestoreCandidatesPermission = userPermissions.includes(PERMISSION.ARCHIVE_RESTORE_CANDIDATES);
  const hasManualResumeScreeningPermission = userPermissions.includes(PERMISSION.MANUAL_RESUME_SCREENING);
  const hasEditScheduledInterviewsPermission = userPermissions.includes(PERMISSION.EDIT_SCHEDULED_INTERVIEWS);
  const hasAddAdditionalCandidateInfoPermission = userPermissions.includes(PERMISSION.ADD_ADDITIONAL_CANDIDATE_INFO);
  const hasManageTopCandidatesPermission = userPermissions.includes(PERMISSION.MANAGE_TOP_CANDIDATES);

  const router = useRouter();

  const paramsPromise = React.use(params);
  const searchParamsPromise = React.use(searchParams);

  // State for candidates data
  const [candidates, setCandidates] = useState<CandidateApplication[]>([]);
  const [loading, setLoading] = useState(false);
  const [loader, setLoader] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [offset, setOffset] = useState(0);
  const [activeDropdown, setActiveDropdown] = useState<number | null>(null);
  const [selectedCandidate, setSelectedCandidate] = useState<CandidateApplication | topCandidateApplication | null>(null);
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [showArchiveModal, setShowArchiveModal] = useState(false); //
  const [searchStr] = useState("");
  const [topCandidates, setTopCandidates] = useState<topCandidateApplication[]>([]);
  const [disable, setDisable] = useState(false);
  const t = useTranslations();
  const dropdownRefs = React.useRef<{ [key: string]: HTMLUListElement | null }>({});
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null);

  const [loadingFullScreen, setFullScreenLoading] = useState(false);

  useEffect(() => {
    if (!Number(paramsPromise.jobId) || !searchParamsPromise.title) {
      router.push(ROUTES.DASHBOARD);
    }
  }, [paramsPromise.jobId]);

  // const observer = useRef<IntersectionObserver | null>(null);

  const fetchTopCandidates = async () => {
    if (!userData?.orgId || !paramsPromise?.jobId) return;

    setLoading(true);
    try {
      const response = await fetchTopCandidatesApplications(Number(paramsPromise.jobId));
      if (response?.data?.success) {
        setTopCandidates(response.data.data);
      } else {
        setTopCandidates([]);
      }
    } catch {
      setTopCandidates([]);
    } finally {
      setLoading(false);
    }
  };

  const handlePromoteDemoteCandidate = async (candidate: CandidateApplication | topCandidateApplication, action: APPLICATION_UPDATE_STATUS) => {
    setDisable(true);
    setFullScreenLoading(true);
    try {
      const payload = {
        candidateId: candidate.candidateId,
        applicationId: candidate.applicationId,
        action,
      };

      const response = await promoteDemoteCandidate(payload as PromoteDemotePayload);

      if (response?.data?.success) {
        if (action === APPLICATION_UPDATE_STATUS.PROMOTED) {
          // Candidate is currently in the 'other candidates' list
          const fromOther = candidate as CandidateApplication;

          // Remove from other candidates
          setCandidates((prev) => prev.filter((c) => c.applicationId !== fromOther.applicationId));

          // Add to top candidates
          const promoted: topCandidateApplication = {
            candidateName: fromOther.candidateName,
            applicationCreatedTs: fromOther.applicationCreatedTs,
            atsScore: fromOther.atsScore,
            applicationId: fromOther.applicationId,
            applicationRankStatus: APPLICATION_UPDATE_STATUS.PROMOTED,
            candidateId: fromOther.candidateId,
            aiReason: fromOther.aiReason,
            aiDecision: fromOther.aiDecision,
            applicationStatus: fromOther.applicationStatus,
            hiringManagerReason: fromOther.hiringManagerReason,
            applicationUpdatedTs: new Date().toISOString(),
            applicationSource: fromOther.applicationSource || "", // Ensure this is set
            job_id: fromOther.job_id || 0, // replace if you have this info
            isTopApplication: fromOther.isTopApplication || false,
          };

          setTopCandidates((prev) => [...prev, promoted]);
        } else if (action === APPLICATION_UPDATE_STATUS.DEMOTED) {
          // Candidate is currently in the 'top candidates' list
          const fromTop = candidate as topCandidateApplication;

          // Remove from top candidates
          setTopCandidates((prev) => prev.filter((c) => c.applicationId !== fromTop.applicationId));

          // Add to other candidates
          const demoted: CandidateApplication = {
            candidateId: fromTop.candidateId,
            candidateName: fromTop.candidateName,
            applicationId: fromTop.applicationId,
            applicationStatus: "",
            applicationSource: "",
            applicationCreatedTs: fromTop.applicationCreatedTs,
            applicationUpdatedTs: new Date().toISOString(),
            isActive: true,
            job_id: 0, // replace if you have this info
            hiring_manager_id: 0,
            hiringManagerReason: "",
            applicationRankStatus: APPLICATION_UPDATE_STATUS.DEMOTED,
            atsScore: fromTop.atsScore,
            aiReason: fromTop.aiReason,
            aiDecision: fromTop.aiDecision,
          };

          setCandidates((prev) => [...prev, demoted]);
        }
      } else {
        toastMessageError(t(response?.data?.message));
      }
    } catch {
    } finally {
      setActiveDropdown(null);
      setDisable(false);
      setFullScreenLoading(false);
    }
  };

  const fetchMoreCandidatesApplications = useCallback(async (currentOffset = 0, reset = false, searchStr: string = "") => {
    if (!userData?.orgId) return;
    setLoader(true);
    try {
      const response = await fetchCandidatesApplications({
        page: currentOffset,
        limit: DEFAULT_LIMIT,
        searchStr: searchStr,
        isActive: true,
        jobId: Number(paramsPromise.jobId),
      });

      console.log("response", response);
      if (response?.data?.success) {
        const newCandidates: CandidateApplication[] = response.data.data.data;

        console.log("newCandidates", newCandidates);

        setCandidates((prev) => (reset ? newCandidates : [...prev, ...newCandidates]));
        if (newCandidates.length < DEFAULT_LIMIT) {
          setHasMore(false);
        } else {
          setHasMore(true);
        }
        setOffset(currentOffset + newCandidates.length);
      } else {
        setHasMore(false);
      }
    } catch {
      setHasMore(false);
    } finally {
      setLoader(false);
    }
  }, []);

  const loadMoreCandidates = () => {
    console.log("loadMoreCandidates called=================>");
    if (!loader && hasMore) fetchMoreCandidatesApplications(offset, false, searchStr);
  };

  // const lastElementRef = useCallback(
  //   (node: HTMLElement | null) => {
  //     if (loading) return;
  //     if (observer.current) observer.current.disconnect();
  //     observer.current = new IntersectionObserver((entries) => {
  //       if (entries[0].isIntersecting && hasMore) loadMoreCandidates();
  //     });
  //     if (node) observer.current.observe(node);
  //   },
  //   [loading, hasMore, offset]
  // );

  useEffect(() => {
    if (userData?.id && userData?.orgId) fetchTopCandidates();
  }, [userData?.id, userData?.orgId]);

  const debouncedHandleSearchInputChange = useCallback(
    debounce(() => {
      if (userData?.id && userData?.orgId) fetchMoreCandidatesApplications(0, true, searchStr);
    }, 1500),
    [userData?.id, userData?.orgId, searchStr, fetchMoreCandidatesApplications]
  );

  useEffect(() => {
    if (userData?.id && userData?.orgId) debouncedHandleSearchInputChange();
    return () => {
      debouncedHandleSearchInputChange.cancel();
    };
  }, [userData?.id, userData?.orgId, debouncedHandleSearchInputChange]);

  const handleArchiveCandidate = (candidate: CandidateApplication | topCandidateApplication) => {
    setSelectedCandidate(candidate);
    setShowArchiveModal(true);
    setActiveDropdown(null);
  };

  const handleReviewCandidate = (candidate: CandidateApplication | topCandidateApplication) => {
    setSelectedCandidate(candidate);
    setShowReviewModal(true);
    setActiveDropdown(null);
  };

  const onCancelReviewModal = () => {
    setShowReviewModal(false);
    setSelectedCandidate(null);
  };

  const onSubmitArchiveReason = async (reason: string) => {
    if (!selectedCandidate) return;
    try {
      await archiveActiveApplication(selectedCandidate.applicationId, false, reason);
      if (selectedCandidate.isTopApplication) {
        setTopCandidates((prev) => prev.filter((c) => c.applicationId !== selectedCandidate.applicationId));
      } else {
        setCandidates((prev) => prev.filter((c) => c.applicationId !== selectedCandidate.applicationId));
      }
      setShowArchiveModal(false);
      setSelectedCandidate(null);
    } catch {}
  };

  const handleStatusChangeSuccess = (candidate: CandidateApplication | topCandidateApplication, status: string) => {
    if (candidate?.isTopApplication) {
      if (status === APPLICATION_STATUS.REJECTED) {
        setTopCandidates((prev) => prev.filter((c) => c.applicationId !== candidate.applicationId));
      } else {
        setTopCandidates((prev) => prev.map((c) => (c.applicationId === candidate.applicationId ? { ...c, applicationStatus: status } : c)));
      }
    } else {
      setCandidates((prev) => prev.map((c) => (c.applicationId === candidate.applicationId ? { ...c, applicationStatus: status } : c)));
    }
    setShowReviewModal(false);
    setSelectedCandidate(null);
  };

  const handleCandidateClick = (candidateId: number) => {
    router.push(`${ROUTES.JOBS.CANDIDATE_PROFILE}/${candidateId}`);
  };
  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        openDropdownId &&
        dropdownRefs.current[openDropdownId] &&
        !dropdownRefs.current[openDropdownId]?.contains(event.target as Node) &&
        !(event.target as Element).closest(".applications-sources-modal")
      ) {
        setOpenDropdownId(null);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [openDropdownId]);

  return (
    <>
      {loadingFullScreen && <FullPageLoader />}

      <section className={`${style.resume_page} ${style.candidates_list_page}`}>
        <div className="container">
          <div className="common-page-header">
            <div className="common-page-head-section">
              <div className="main-heading">
                <h2>
                  <BackArrowIcon onClick={() => router.back()} />
                  {t("candidates_for")} <span>{searchParamsPromise.title}</span>
                </h2>
                <div className="right-action">
                  {hasManualResumeScreeningPermission && (
                    <Button
                      className="primary-btn rounded-md button-sm"
                      onClick={() =>
                        router.push(
                          `${ROUTES.SCREEN_RESUME.MANUAL_CANDIDATE_UPLOAD}/${paramsPromise.jobId}?title=${searchParamsPromise.title}&jobUniqueId=${searchParamsPromise.jobUniqueId}`
                        )
                      }
                    >
                      {t("add_candidate")}
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className={style.candidates_list_section}>
            <div className={style.section_name}>
              <h3> {t("top_ten_candidates")} </h3>
              <p> {t("based_on_interview_date")} </p>
            </div>
            <div className={`table-responsive ${topCandidates.length <3 ? "min-data" : ""}`}>
              <table className="table overflow-auto mb-0">
                <thead>
                  <tr>
                    <th style={{ width: "20%" }}> {t("candidate_name")} </th>
                    <th style={{ width: "20%" }}> {t("date_submitted")} </th>
                    <th style={{ width: "20%" }} className="text-center">
                      {t("ats_score")}
                    </th>
                    {/* <th> {t("lined_up_for")} </th> */}
                    <th style={{ width: "20%" }} className="text-center">
                      {t("candidates_analysis")}
                    </th>

                    <th style={{ width: "20%" }} className="text-center">
                      {" "}
                      {t("actions")}{" "}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {topCandidates.length > 0 ? (
                    topCandidates.map((candidate, index) => {
                      const isPromoted = candidate.applicationRankStatus === APPLICATION_UPDATE_STATUS.PROMOTED;
                      const isDemoted = candidate.applicationRankStatus === APPLICATION_UPDATE_STATUS.DEMOTED;
                      const dotClass = isPromoted ? "green-dot" : isDemoted ? "red-dot" : "";
                      const statusClass =
                        candidate.applicationStatus === APPLICATION_STATUS.APPROVED
                          ? "color-success"
                          : candidate.applicationStatus === APPLICATION_STATUS.REJECTED
                            ? "color-danger"
                            : "color-dark";
                      return (
                        <tr key={candidate.candidateId}>
                          <td style={{ width: "20%" }}>
                            <div
                              onClick={() => handleCandidateClick(candidate.candidateId)}
                              className={`color-primary cursor-pointer text-decoration-underline ${dotClass} d-inline`}
                            >
                              {index + 1}. {toTitleCase(candidate.candidateName)}
                            </div>
                          </td>
                          <td style={{ width: "20%" }}>
                            {candidate.applicationCreatedTs ? dayjs(candidate.applicationCreatedTs).format("MMM D, YYYY") : "Not Available"}
                          </td>
                          <td style={{ width: "20%" }} className="text-center">
                            {candidate.atsScore}
                          </td>
                          <td style={{ width: "20%", textAlign: "center" }} className={statusClass}>
                            {candidate.applicationStatus}
                          </td>

                          {/* <td>{candidate.applicationRankStatus}</td> */}
                          <td style={{ width: "20%" }} align="center" className="position-relative">
                            <div onClick={() => (activeDropdown ? setActiveDropdown(null) : setActiveDropdown(candidate.candidateId))}>
                              <Button className="clear-btn p-0">
                                <ThreeDotsIcon />
                              </Button>
                              {activeDropdown === candidate.candidateId && (
                                <ul className="custom-dropdown">
                                  {candidate.applicationStatus === APPLICATION_STATUS.APPROVED && hasEditScheduledInterviewsPermission && (
                                    <li
                                      onClick={() =>
                                        router.push(
                                          `${ROUTES.INTERVIEW.SCHEDULE_INTERVIEW}?applicationId=${candidate.applicationId}&candidateName=${encodeURIComponent(candidate.candidateName)}&title=${encodeURIComponent(searchParamsPromise.title)}&jobId=${paramsPromise.jobId}&jobUniqueId=${encodeURIComponent(searchParamsPromise.jobUniqueId)}`
                                        )
                                      }
                                    >
                                      {t("schedule_interview")}{" "}
                                    </li>
                                  )}
                                  {hasAddAdditionalCandidateInfoPermission && (
                                    <li
                                      onClick={() => router.push(`${ROUTES.INTERVIEW.ADD_CANDIDATE_INFO}?applicationId=${candidate.applicationId}`)}
                                    >
                                      {t("add_candidates_info")}{" "}
                                    </li>
                                  )}
                                  {[APPLICATION_STATUS.PENDING, APPLICATION_STATUS.ON_HOLD].includes(candidate.applicationStatus) && (
                                    <li onClick={() => handleReviewCandidate(candidate)}>{t("analyze_candidate_resume")}</li>
                                  )}
                                  {hasManageTopCandidatesPermission && (
                                    <li
                                      onClick={
                                        !disable
                                          ? () =>
                                              handlePromoteDemoteCandidate(
                                                candidate as unknown as CandidateApplication,
                                                APPLICATION_UPDATE_STATUS.DEMOTED
                                              )
                                          : undefined
                                      }
                                    >
                                      {t("demote_candidate")}
                                    </li>
                                  )}
                                  {hasArchiveRestoreCandidatesPermission && (
                                    <li onClick={() => handleArchiveCandidate(candidate)}>{t("archive_candidate")}</li>
                                  )}
                                </ul>
                              )}
                            </div>
                          </td>
                        </tr>
                      );
                    })
                  ) : !loading ? (
                    <tr>
                      <td colSpan={5} className="text-center">
                        {t(topCandidates.length ? "no_more_candidates_to_fetch" : "no_candidates_found")}
                      </td>
                    </tr>
                  ) : null}
                </tbody>
                {loading && <TableSkeleton rows={3} cols={5} colWidths="120,80,100,24,24" />}
              </table>
            </div>
          </div>

          {/* Only show Other Candidates section if there are candidates */}
          {candidates.length > 0 && (
            <div className={style.candidates_list_section}>
              <div className={`${style.section_name} d-flex align-items-center justify-content-between`}>
                <div>
                  <h3>{t("other_candidates")}</h3>
                  <p>{t("rancked_by_resume")}</p>
                </div>
                {/* <div className="right-action w-25">
                  <InputWrapper className="mb-0 w-100 search-input">
                    <div className="icon-align right">
                      <Textbox
                        className="form-control w-100"
                        control={control}
                        name="search"
                        type="text"
                        placeholder="Search using name"
                        // onChange={(e) => setSearchStr(e.target.value)}
                      >
                        <InputWrapper.Icon>
                          <SearchIcon />
                        </InputWrapper.Icon>
                      </Textbox>
                    </div>
                  </InputWrapper>
                </div> */}
              </div>
              <div className={`table-responsive ${candidates.length <3 ? "min-data" : ""}`} id="scrollableCandidatesTableDiv">
                <InfiniteScroll
                  dataLength={candidates.length}
                  next={() => loadMoreCandidates()}
                  hasMore={hasMore}
                  height={window.innerHeight - 300}
                  loader={
                    loader && (
                      <table className="table w-100">
                        <TableSkeleton rows={3} cols={5} colWidths="120,80,100,24,24" />
                      </table>
                    )
                  }
                  endMessage={
                    !loader && candidates.length ? (
                      <table className="table w-100">
                        <tbody>
                          <tr>
                            <td colSpan={5} style={{ textAlign: "center", backgroundColor: "#fff" }}>
                              {t("no_more_candidates_to_fetch")}
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    ) : null
                  }
                >
                  <table className="table overflow-auto mb-0">
                    <thead>
                      <tr>
                        <th style={{ width: "20%" }}>{t("candidate_name")}</th>
                        <th style={{ width: "20%" }}>{t("date_submitted")}</th>
                        {/* <th>{t("source")}</th> */}
                        <th style={{ width: "20%" }} className="text-center">
                          {t("ats_score")}
                        </th>

                        <th style={{ width: "20%" }} className="text-center">
                          {t("candidates_analysis")}
                        </th>
                        <th style={{ width: "20%" }} className="text-center">
                          {t("actions")}
                        </th>
                      </tr>
                    </thead>
                    {candidates.length > 0 ? (
                      <tbody id="scrollableCandidatesTableBody">
                        {candidates.map((candidate, index) => {
                          const statusClass =
                            candidate.applicationStatus === APPLICATION_STATUS.APPROVED
                              ? "color-success text-center"
                              : candidate.applicationStatus === APPLICATION_STATUS.REJECTED
                                ? "color-danger text-center"
                                : "color-dark text-center";

                          const isPromoted = candidate.applicationRankStatus === APPLICATION_UPDATE_STATUS.PROMOTED;
                          const isDemoted = candidate.applicationRankStatus === APPLICATION_UPDATE_STATUS.DEMOTED;
                          const dotClass = isPromoted ? "green-dot" : isDemoted ? "red-dot" : "";

                          return (
                            <tr
                              key={candidate.candidateId}
                              // ref={lastElementRef}
                            >
                              <td>
                                <div
                                  onClick={() => handleCandidateClick(candidate.candidateId)}
                                  className={`color-primary cursor-pointer text-decoration-underline ${dotClass} d-inline`}
                                >
                                  {index + 1}. {toTitleCase(candidate.candidateName) || "Candidate Name"}
                                </div>
                              </td>
                              <td>
                                {candidate.applicationCreatedTs ? dayjs(candidate.applicationCreatedTs).format("MMM D, YYYY") : "Not Available"}
                              </td>
                              <td className="text-center">{candidate.atsScore ?? "N/A"}</td>
                              <td className={statusClass}>{candidate.applicationStatus}</td>
                              <td align="center" className="position-relative">
                                <div onClick={() => (activeDropdown ? setActiveDropdown(null) : setActiveDropdown(candidate.candidateId))}>
                                  <Button className="clear-btn p-0">
                                    <ThreeDotsIcon />
                                  </Button>
                                  {activeDropdown === candidate.candidateId && (
                                    <ul
                                      className="custom-dropdown"
                                      ref={(element) => {
                                        if (element) {
                                          dropdownRefs.current[String(candidate.candidateId)] = element;
                                        }
                                      }}
                                    >
                                      {candidate.applicationStatus === APPLICATION_STATUS.APPROVED && hasEditScheduledInterviewsPermission && (
                                        <li
                                          onClick={() =>
                                            router.push(
                                              `${ROUTES.INTERVIEW.SCHEDULE_INTERVIEW}?applicationId=${candidate.applicationId}&candidateName=${encodeURIComponent(candidate.candidateName)}&title=${encodeURIComponent(searchParamsPromise.title)}&jobId=${paramsPromise.jobId}&jobUniqueId=${encodeURIComponent(searchParamsPromise.jobUniqueId)}`
                                            )
                                          }
                                        >
                                          {t("schedule_interview")}
                                        </li>
                                      )}
                                      {hasAddAdditionalCandidateInfoPermission && (
                                        <li
                                          onClick={() =>
                                            router.push(`${ROUTES.INTERVIEW.ADD_CANDIDATE_INFO}?applicationId=${candidate.applicationId}`)
                                          }
                                        >
                                          {t("add_candidates_info")}
                                        </li>
                                      )}
                                      {[APPLICATION_STATUS.PENDING, APPLICATION_STATUS.ON_HOLD].includes(candidate.applicationStatus) && (
                                        <li onClick={() => handleReviewCandidate(candidate)}>{t("analyze_candidate_resume")}</li>
                                      )}
                                      {/* <li>{t("analyze_candidate_resume")}</li> */}
                                      {candidate.applicationStatus !== APPLICATION_STATUS.REJECTED && hasManageTopCandidatesPermission && (
                                        <li
                                          onClick={
                                            !disable ? () => handlePromoteDemoteCandidate(candidate, APPLICATION_UPDATE_STATUS.PROMOTED) : undefined
                                          }
                                          style={disable ? { pointerEvents: "none", opacity: 0.5 } : {}}
                                        >
                                          {t("promote_candidate")}
                                        </li>
                                      )}
                                      {hasArchiveRestoreCandidatesPermission && (
                                        <li onClick={() => handleArchiveCandidate(candidate)}>{t("archive_candidate")}</li>
                                      )}
                                    </ul>
                                  )}
                                </div>
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    ) : (
                      !loading && (
                        <tbody>
                          <tr>
                            <td colSpan={5} style={{ textAlign: "center" }}>
                              {t("no_candidates_found")}
                            </td>
                          </tr>
                        </tbody>
                      )
                    )}
                  </table>
                </InfiniteScroll>
              </div>
            </div>
          )}
        </div>
      </section>

      {showReviewModal && selectedCandidate && (
        <CandidateApproveRejectModal onClickCancel={onCancelReviewModal} onSuccess={handleStatusChangeSuccess} candidate={selectedCandidate} />
      )}

      {showArchiveModal && selectedCandidate && (
        <ArchiveCandidateModal
          onClickCancel={() => setShowArchiveModal(false)}
          applicationId={selectedCandidate.applicationId}
          jobId={Number(paramsPromise.jobId)}
          onSuccess={(reason) => onSubmitArchiveReason(reason)}
        />
      )}
    </>
  );
};

export default CandidatesList;
