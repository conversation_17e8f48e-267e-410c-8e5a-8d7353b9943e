module.exports = {

"[externals]/next/dist/compiled/next-server/app-page.runtime.dev.js [external] (next/dist/compiled/next-server/app-page.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/redux/slices/jobSkillsSlice.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "clearSkillsData": (()=>clearSkillsData),
    "default": (()=>__TURBOPACK__default__export__),
    "jobSkillsSlice": (()=>jobSkillsSlice),
    "selectCareerSkills": (()=>selectCareerSkills),
    "selectCultureSpecificSkills": (()=>selectCultureSpecificSkills),
    "selectJobSkillsState": (()=>selectJobSkillsState),
    "selectRoleSpecificSkills": (()=>selectRoleSpecificSkills),
    "setSkillsData": (()=>setSkillsData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-ssr] (ecmascript) <locals>");
;
// Define the initial state using that type
const initialState = {
    careerSkills: [],
    roleSpecificSkills: [],
    cultureSpecificSkills: []
};
const jobSkillsSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: "jobSkills",
    initialState,
    reducers: {
        setSkillsData: (state, action)=>{
            if (action.payload.careerSkills) {
                state.careerSkills = action.payload.careerSkills;
            }
            if (action.payload.roleSpecificSkills) {
                state.roleSpecificSkills = action.payload.roleSpecificSkills;
            }
            if (action.payload.cultureSpecificSkills) {
                state.cultureSpecificSkills = action.payload.cultureSpecificSkills;
            }
        },
        clearSkillsData: (state)=>{
            state.careerSkills = [];
            state.roleSpecificSkills = [];
            state.cultureSpecificSkills = [];
        }
    }
});
const { setSkillsData, clearSkillsData } = jobSkillsSlice.actions;
const selectJobSkillsState = (state)=>state.jobSkills;
const selectCareerSkills = (state)=>state.jobSkills.careerSkills;
const selectRoleSpecificSkills = (state)=>state.jobSkills.roleSpecificSkills;
const selectCultureSpecificSkills = (state)=>state.jobSkills.cultureSpecificSkills;
const __TURBOPACK__default__export__ = jobSkillsSlice.reducer;
}}),
"[project]/src/constants/commonConstants.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ACCESS_TOKEN_KEY": (()=>ACCESS_TOKEN_KEY),
    "ACTIVE": (()=>ACTIVE),
    "ASSESSMENT_INSTRUCTIONS": (()=>ASSESSMENT_INSTRUCTIONS),
    "DEFAULT_LIMIT": (()=>DEFAULT_LIMIT),
    "DEFAULT_MCQ_OPTIONS": (()=>DEFAULT_MCQ_OPTIONS),
    "DEFAULT_TRUE_FALSE_OPTIONS": (()=>DEFAULT_TRUE_FALSE_OPTIONS),
    "EMAIL_REGEX": (()=>EMAIL_REGEX),
    "EMPTY_CONTENT_PATTERNS": (()=>EMPTY_CONTENT_PATTERNS),
    "FILE_EXTENSION": (()=>FILE_EXTENSION),
    "IMAGE_EXTENSIONS": (()=>IMAGE_EXTENSIONS),
    "INTERVIEW_SCHEDULE_ROUND_TYPE": (()=>INTERVIEW_SCHEDULE_ROUND_TYPE),
    "MAX_IMAGE_SIZE": (()=>MAX_IMAGE_SIZE),
    "ONE_TO_ONE_INTERVIEW_INSTRUCTIONS": (()=>ONE_TO_ONE_INTERVIEW_INSTRUCTIONS),
    "OPTION_ID": (()=>OPTION_ID),
    "PASSWORD_REGEX": (()=>PASSWORD_REGEX),
    "PDF_ADDITIONAL_SUBMISSION_LIMIT": (()=>PDF_ADDITIONAL_SUBMISSION_LIMIT),
    "PDF_FILE_NAME": (()=>PDF_FILE_NAME),
    "PDF_FILE_SIZE_LIMIT": (()=>PDF_FILE_SIZE_LIMIT),
    "PDF_FILE_TYPE": (()=>PDF_FILE_TYPE),
    "PERMISSION": (()=>PERMISSION),
    "PERMISSIONS_COOKIES_KEY": (()=>PERMISSIONS_COOKIES_KEY),
    "QUESTION_TYPE": (()=>QUESTION_TYPE),
    "QUESTION_TYPES": (()=>QUESTION_TYPES),
    "QuestionType": (()=>QuestionType),
    "S3_PATHS": (()=>S3_PATHS),
    "SKILL_CONSTANTS": (()=>SKILL_CONSTANTS),
    "ScheduleInterviewformSubmissionType": (()=>ScheduleInterviewformSubmissionType),
    "TOKEN_EXPIRED": (()=>TOKEN_EXPIRED),
    "VIDEO_CALL_INTERVIEW_INSTRUCTIONS": (()=>VIDEO_CALL_INTERVIEW_INSTRUCTIONS),
    "commonConstants": (()=>commonConstants),
    "initialState": (()=>initialState)
});
const ACCESS_TOKEN_KEY = "__ATK__";
const EMAIL_REGEX = /^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$/;
const PASSWORD_REGEX = /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[^a-zA-Z0-9])(?!.*\s).{8,16}$/;
const MAX_IMAGE_SIZE = 5242880;
const ScheduleInterviewformSubmissionType = {
    SCHEDULE: "schedule",
    UPDATE: "update"
};
const S3_PATHS = {
    PROFILE_IMAGE: "profile-images/:path"
};
const ONE_TO_ONE_INTERVIEW_INSTRUCTIONS = [
    "Arrive at the interview location on time with a government-issued ID.",
    "Ensure your phone is on silent mode and distractions are minimized.",
    "Bring a printed copy of your resume and any supporting documents.",
    "Dress professionally and maintain proper body language.",
    "Listen carefully, answer honestly, and ask for clarification if needed.",
    "Respect the interview flow and do not interrupt the interviewer.",
    "Take brief notes if necessary, but focus on active conversation.",
    "If you need assistance or face any issues, notify the interview coordinator."
];
const VIDEO_CALL_INTERVIEW_INSTRUCTIONS = [
    "Join the interview on time using the link provided.",
    "Ensure a stable internet connection and a quiet, well-lit space.",
    "Test your camera, microphone, and audio settings in advance.",
    "Keep your video on unless instructed otherwise by the interviewer.",
    "Minimize background noise and avoid multitasking during the session.",
    "Use headphones if possible for better audio clarity.",
    "Be attentive, respond clearly, and maintain professional posture.",
    "Contact support if you face technical difficulties before or during the interview."
];
const PERMISSION = {
    CREATE_OR_EDIT_JOB_POST: "create-or-edit-job-post",
    SCHEDULE_CONDUCT_INTERVIEWS: "schedule-conduct-interviews",
    VIEW_HIRED_CANDIDATES: "view-hired-candidates",
    ARCHIVE_RESTORE_CANDIDATES: "archive-restore-candidates",
    ARCHIVE_RESTORE_JOB_POSTS: "archive-restore-job-posts",
    MANUAL_RESUME_SCREENING: "manual-resume-screening",
    EDIT_SCHEDULED_INTERVIEWS: "edit-scheduled-interviews",
    ADD_ADDITIONAL_CANDIDATE_INFO: "add-additional-candidate-info",
    ADD_OR_EDIT_INTERVIEW_NOTES: "add-or-edit-interview-notes",
    MANAGE_TOP_CANDIDATES: "manage-top-candidates",
    MANAGE_PRE_INTERVIEW_QUESTIONS: "manage-pre-interview-questions",
    CREATE_FINAL_ASSESSMENT: "create-final-assessment",
    VIEW_FINAL_ASSESSMENT: "view-final-assessment",
    VIEW_CANDIDATE_PROFILE_SUMMARY: "view-candidate-profile-summary",
    HIRE_CANDIDATE: "hire-candidate",
    CREATE_NEW_ROLE: "create-new-role",
    MANAGE_USER_PERMISSIONS: "manage-user-permissions",
    CREATE_NEW_DEPARTMENT: "create-new-department",
    ADD_INTERVIEW_PARTICIPANTS: "add-interview-participants",
    VIEW_SUBSCRIPTION_PLAN: "view-subscription-plan",
    MANAGE_SUBSCRIPTIONS: "manage-subscriptions",
    VIEW_AUDIT_LOGS_UPCOMING: "view-audit-logs-upcoming",
    VIEW_ALL_SCHEDULED_INTERVIEWS: "view-all-scheduled-interviews"
};
const SKILL_CONSTANTS = {
    REQUIRED_ROLE_SKILLS: 10,
    REQUIRED_CULTURE_SKILLS: 5
};
const commonConstants = {
    finalAssessmentId: "finalAssessmentId",
    token: "token",
    isShared: "isShared",
    isSubmitted: "isSubmitted",
    jobId: "jobId",
    jobApplicationId: "jobApplicationId"
};
const QuestionType = {
    MCQ: "mcq",
    TRUE_FALSE: "true_false"
};
const OPTION_ID = {
    A: "A",
    B: "B",
    C: "C",
    D: "D",
    TRUE: "true",
    FALSE: "false"
};
const QUESTION_TYPE = {
    MCQ: "mcq",
    TRUE_FALSE: "true_false"
};
const DEFAULT_MCQ_OPTIONS = [
    {
        id: OPTION_ID.A,
        text: ""
    },
    {
        id: OPTION_ID.B,
        text: ""
    },
    {
        id: OPTION_ID.C,
        text: ""
    },
    {
        id: OPTION_ID.D,
        text: ""
    }
];
const DEFAULT_TRUE_FALSE_OPTIONS = [
    {
        id: OPTION_ID.TRUE,
        text: "True"
    },
    {
        id: OPTION_ID.FALSE,
        text: "False"
    }
];
const INTERVIEW_SCHEDULE_ROUND_TYPE = [
    {
        label: "One-On-One",
        value: "One-On-One"
    },
    {
        label: "Video Call",
        value: "Video Call"
    }
];
const QUESTION_TYPES = {
    ROLE_SPECIFIC: "role_specific",
    CULTURE_SPECIFIC: "culture_specific",
    CAREER_BASED: "career_based"
};
const EMPTY_CONTENT_PATTERNS = [
    "<p><br></p>",
    "<p></p>",
    "<div><br></div>",
    "<div></div>",
    "<p>&nbsp;</p>"
];
const initialState = {
    title: "",
    employment_type: "",
    department_id: "",
    salary_range: "",
    salary_cycle: "",
    location_type: "",
    state: "",
    city: "",
    role_overview: "",
    experience_level: "",
    responsibilities: "",
    educations_requirement: "",
    certifications: undefined,
    skills_and_software_expertise: "",
    experience_required: "",
    ideal_candidate_traits: "",
    about_company: "",
    perks_benefits: undefined,
    tone_style: "",
    additional_info: undefined,
    compliance_statement: [],
    show_compliance: false,
    hiring_type: ""
};
const FILE_EXTENSION = [
    "pdf",
    "plain",
    "csv",
    "vnd.ms-excel.sheet.macroEnabled.12",
    "vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "vnd.openxmlformats-officedocument.wordprocessingml.document",
    "vnd.openxmlformats-officedocument.presentationml.presentation"
];
const ACTIVE = "active";
const TOKEN_EXPIRED = "Session Expired! Please log in again.";
const DEFAULT_LIMIT = 15;
const IMAGE_EXTENSIONS = [
    "png",
    "jpg",
    "jpeg",
    "gif",
    "webp"
];
const ASSESSMENT_INSTRUCTIONS = {
    instructions: [
        "Do not refresh or close the browser",
        "Check your internet connection",
        "Ensure a distraction-free environment",
        "Click 'Submit' only once when finished",
        "Read each question carefully",
        "Manage your time efficiently",
        "Avoid any form of plagiarism",
        "Reach out to support if needed"
    ]
};
const PERMISSIONS_COOKIES_KEY = "permissions_data";
const PDF_FILE_NAME = "pdf";
const PDF_FILE_TYPE = "application/pdf";
const PDF_FILE_SIZE_LIMIT = 5 * 1024 * 1024;
const PDF_ADDITIONAL_SUBMISSION_LIMIT = 10854484;
}}),
"[project]/src/redux/slices/jobDetailsSlice.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "clearJobDetails": (()=>clearJobDetails),
    "default": (()=>__TURBOPACK__default__export__),
    "jobDetailsSlice": (()=>jobDetailsSlice),
    "selectJobDetails": (()=>selectJobDetails),
    "setJobDetails": (()=>setJobDetails),
    "updateJobDetail": (()=>updateJobDetail)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/commonConstants.ts [app-ssr] (ecmascript)");
;
;
const jobDetailsSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: "jobDetails",
    initialState: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["initialState"],
    reducers: {
        // Set all job details at once
        setJobDetails: (state, action)=>{
            return {
                ...state,
                ...action.payload
            };
        },
        // Clear job details
        clearJobDetails: ()=>{
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["initialState"];
        },
        // Update a specific field in job details
        updateJobDetail: (state, action)=>{
            const { field, value } = action.payload;
            state[field] = value;
        }
    }
});
const { setJobDetails, clearJobDetails, updateJobDetail } = jobDetailsSlice.actions;
const selectJobDetails = (state)=>state.jobDetails;
const __TURBOPACK__default__export__ = jobDetailsSlice.reducer;
}}),
"[project]/src/redux/slices/allSkillsSlice.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "allSkillsSlice": (()=>allSkillsSlice),
    "default": (()=>__TURBOPACK__default__export__),
    "fetchSkillsFailure": (()=>fetchSkillsFailure),
    "fetchSkillsStart": (()=>fetchSkillsStart),
    "fetchSkillsSuccess": (()=>fetchSkillsSuccess),
    "selectAllSkills": (()=>selectAllSkills),
    "selectSkillsByCategory": (()=>selectSkillsByCategory),
    "selectSkillsError": (()=>selectSkillsError),
    "selectSkillsLoading": (()=>selectSkillsLoading),
    "updateSkillItem": (()=>updateSkillItem)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-ssr] (ecmascript) <locals>");
;
// Define the initial state
const initialState = {
    categories: [],
    loading: false,
    error: null
};
const allSkillsSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: "allSkills",
    initialState,
    reducers: {
        fetchSkillsStart: (state)=>{
            state.loading = true;
            state.error = null;
        },
        fetchSkillsSuccess: (state, action)=>{
            state.categories = action.payload;
            state.loading = false;
            state.error = null;
        },
        fetchSkillsFailure: (state, action)=>{
            state.loading = false;
            state.error = action.payload;
        },
        updateSkillItem: (state, action)=>{
            const { categoryType, skillId, updatedSkill } = action.payload;
            const categoryIndex = state.categories.findIndex((cat)=>cat.type === categoryType);
            if (categoryIndex !== -1) {
                const skillIndex = state.categories[categoryIndex].items.findIndex((item)=>item.id === skillId);
                if (skillIndex !== -1) {
                    state.categories[categoryIndex].items[skillIndex] = {
                        ...state.categories[categoryIndex].items[skillIndex],
                        ...updatedSkill
                    };
                }
            }
        }
    }
});
const { fetchSkillsStart, fetchSkillsSuccess, fetchSkillsFailure, updateSkillItem } = allSkillsSlice.actions;
const selectAllSkills = (state)=>state.allSkills.categories;
const selectSkillsLoading = (state)=>state.allSkills.loading;
const selectSkillsError = (state)=>state.allSkills.error;
const selectSkillsByCategory = (categoryType)=>(state)=>state.allSkills.categories.find((cat)=>cat.type === categoryType)?.items || [];
const __TURBOPACK__default__export__ = allSkillsSlice.reducer;
}}),
"[project]/src/redux/slices/authSlice.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "authSlice": (()=>authSlice),
    "default": (()=>__TURBOPACK__default__export__),
    "selectDepartment": (()=>selectDepartment),
    "selectPermissions": (()=>selectPermissions),
    "selectProfileData": (()=>selectProfileData),
    "selectRole": (()=>selectRole),
    "setAuthData": (()=>setAuthData),
    "setDepartment": (()=>setDepartment),
    "setPermissions": (()=>setPermissions),
    "setRole": (()=>setRole),
    "updateUserProfileData": (()=>updateUserProfileData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-ssr] (ecmascript) <locals>");
;
const initialState = {
    authData: {
        id: -1,
        account_type: "",
        email: "",
        isVerified: false,
        sms_notification: false,
        allow_notification: false,
        is_deleted: false,
        image: "",
        orgId: -1,
        departmentId: -1,
        organizationName: "",
        organizationCode: "",
        createdTs: "",
        first_name: "",
        last_name: ""
    },
    department: null,
    role: null,
    permissions: []
};
const authSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: "auth",
    initialState,
    reducers: {
        setAuthData: (state, action)=>{
            state.authData = action.payload;
        },
        setRole: (state, action)=>{
            state.role = action.payload;
        },
        setDepartment: (state, action)=>{
            state.department = action.payload;
        },
        setPermissions: (state, action)=>{
            state.permissions = action.payload;
        },
        updateUserProfileData: (state, action)=>{
            if (state.authData) {
                const { first_name, last_name, image } = action.payload;
                // Update firstName and lastName separately
                if (first_name !== undefined) {
                    state.authData.first_name = first_name;
                }
                if (last_name !== undefined) {
                    state.authData.last_name = last_name;
                }
                // Update image if provided
                if (image !== undefined) {
                    state.authData.image = image;
                }
            }
        }
    }
});
const selectRole = (state)=>state.auth.role;
const selectDepartment = (state)=>state.auth.department;
const selectPermissions = (state)=>state.auth.permissions;
const selectProfileData = (state)=>state.auth.authData;
const { setAuthData, setRole, setDepartment, setPermissions, updateUserProfileData } = authSlice.actions;
const __TURBOPACK__default__export__ = authSlice.reducer;
}}),
"[project]/src/redux/slices/jobRequirementSlice.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "clearJobRequirement": (()=>clearJobRequirement),
    "default": (()=>__TURBOPACK__default__export__),
    "jobRequirementSlice": (()=>jobRequirementSlice),
    "selectJobRequirement": (()=>selectJobRequirement),
    "setJobRequirement": (()=>setJobRequirement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-ssr] (ecmascript) <locals>");
;
const initialState = {
    content: "",
    isGenerated: false,
    generatedAt: null
};
const jobRequirementSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: "jobRequirement",
    initialState,
    reducers: {
        // Set job requirement content
        setJobRequirement: (state, action)=>{
            state.content = action.payload;
            state.isGenerated = true;
            state.generatedAt = new Date().toISOString();
        },
        // Clear job requirement data
        clearJobRequirement: (state)=>{
            state.content = "";
            state.isGenerated = false;
            state.generatedAt = null;
        }
    }
});
const { setJobRequirement, clearJobRequirement } = jobRequirementSlice.actions;
const selectJobRequirement = (state)=>state.jobRequirement;
const __TURBOPACK__default__export__ = jobRequirementSlice.reducer;
}}),
"[project]/src/redux/slices/interviewSlice.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "clearInterview": (()=>clearInterview),
    "default": (()=>__TURBOPACK__default__export__),
    "interviewSlice": (()=>interviewSlice),
    "setInterviewQuestions": (()=>setInterviewQuestions),
    "setInterviewStaticInformation": (()=>setInterviewStaticInformation),
    "updateQuestionAnswer": (()=>updateQuestionAnswer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/commonConstants.ts [app-ssr] (ecmascript)");
;
;
const initialState = {
    roleSpecificQuestions: {},
    cultureSpecificQuestions: {},
    careerBasedQuestions: {
        questions: [],
        score: 0
    },
    interviewStaticInformation: {
        oneToOneInterviewInstructions: [],
        videoCallInterviewInstructions: [],
        startumDescription: []
    }
};
const interviewSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: "interview",
    initialState,
    reducers: {
        setInterviewQuestions: (state, action)=>{
            // Handle role-specific questions
            if (action.payload.roleSpecificQuestions !== undefined) {
                state.roleSpecificQuestions = action.payload.roleSpecificQuestions;
            }
            // Handle culture-specific questions
            if (action.payload.cultureSpecificQuestions !== undefined) {
                state.cultureSpecificQuestions = action.payload.cultureSpecificQuestions;
            }
            // Handle career-based questions
            if (action.payload.careerBasedQuestions !== undefined) {
                state.careerBasedQuestions = action.payload.careerBasedQuestions;
            }
        },
        setInterviewStaticInformation: (state, action)=>{
            state.interviewStaticInformation = action.payload;
        },
        updateQuestionAnswer: (state, action)=>{
            const { questionType, category, questionAnswers, stratumScore, interviewerName } = action.payload;
            // Create a Map for O(1) lookups
            const answerMap = new Map(questionAnswers.map((qa)=>[
                    qa.questionId,
                    qa.answer
                ]));
            switch(questionType){
                case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].CAREER_BASED:
                    // Update answers
                    state.careerBasedQuestions.questions = state.careerBasedQuestions.questions.map((question)=>{
                        const answer = answerMap.get(question.id);
                        if (answer !== undefined) {
                            return {
                                ...question,
                                answer
                            };
                        }
                        return question;
                    });
                    // Update score
                    state.careerBasedQuestions.score = stratumScore;
                    break;
                case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].ROLE_SPECIFIC:
                    if (category) {
                        // Initialize category if it doesn't exist
                        if (!state.roleSpecificQuestions[category]) {
                            state.roleSpecificQuestions[category] = {
                                questions: [],
                                score: 0
                            };
                        }
                        // Update answers
                        state.roleSpecificQuestions[category].questions = state.roleSpecificQuestions[category].questions.map((question)=>{
                            const answer = answerMap.get(question.id);
                            if (answer !== undefined) {
                                return {
                                    ...question,
                                    answer
                                };
                            }
                            return question;
                        });
                        // Update score and interviewer name
                        state.roleSpecificQuestions[category].score = stratumScore;
                        state.roleSpecificQuestions[category].interviewerName = interviewerName;
                    }
                    break;
                case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].CULTURE_SPECIFIC:
                    if (category) {
                        // Initialize category if it doesn't exist
                        if (!state.cultureSpecificQuestions[category]) {
                            state.cultureSpecificQuestions[category] = {
                                questions: [],
                                score: 0
                            };
                        }
                        // Update answers
                        state.cultureSpecificQuestions[category].questions = state.cultureSpecificQuestions[category].questions.map((question)=>{
                            const answer = answerMap.get(question.id);
                            if (answer !== undefined) {
                                return {
                                    ...question,
                                    answer
                                };
                            }
                            return question;
                        });
                        // Update score and interviewer name
                        state.cultureSpecificQuestions[category].score = stratumScore;
                        state.cultureSpecificQuestions[category].interviewerName = interviewerName;
                    }
                    break;
            }
        },
        clearInterview: (state)=>{
            // Reset state to initial values
            state.roleSpecificQuestions = initialState.roleSpecificQuestions;
            state.cultureSpecificQuestions = initialState.cultureSpecificQuestions;
            state.careerBasedQuestions = initialState.careerBasedQuestions;
        }
    }
});
const { setInterviewQuestions, updateQuestionAnswer, clearInterview, setInterviewStaticInformation } = interviewSlice.actions;
const __TURBOPACK__default__export__ = interviewSlice.reducer;
}}),
"[project]/src/redux/store.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "persistor": (()=>persistor),
    "store": (()=>store)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/es/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistStore$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__persistStore$3e$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/es/persistStore.js [app-ssr] (ecmascript) <export default as persistStore>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistReducer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__persistReducer$3e$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/es/persistReducer.js [app-ssr] (ecmascript) <export default as persistReducer>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/es/constants.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$lib$2f$storage$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/lib/storage/index.js [app-ssr] (ecmascript)"); // defaults to localStorage for web
// Import the reducers directly to avoid circular dependency
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$jobSkillsSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/slices/jobSkillsSlice.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$jobDetailsSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/slices/jobDetailsSlice.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$allSkillsSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/slices/allSkillsSlice.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/slices/authSlice.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$jobRequirementSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/slices/jobRequirementSlice.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$interviewSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/slices/interviewSlice.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
// Configure persist options for job skills slice
const jobSkillsPersistConfig = {
    key: "jobSkills",
    storage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$lib$2f$storage$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
};
// Configure persist options for job details slice
const jobDetailsPersistConfig = {
    key: "jobDetails",
    storage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$lib$2f$storage$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
};
// Configure persist options for all skills slice
const allSkillsPersistConfig = {
    key: "allSkills",
    storage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$lib$2f$storage$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    blacklist: [
        "loading",
        "error"
    ]
};
// Configure persist options for auth slice
const authPersistConfig = {
    key: "auth",
    storage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$lib$2f$storage$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
};
// Configure persist options for job requirement slice
const jobRequirementPersistConfig = {
    key: "jobRequirement",
    storage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$lib$2f$storage$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
};
// Configure persist options for interview questions slice
const interviewPersistConfig = {
    key: "interview",
    storage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$lib$2f$storage$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
};
// Create persisted reducers
const persistedJobSkillsReducer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistReducer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__persistReducer$3e$__["persistReducer"])(jobSkillsPersistConfig, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$jobSkillsSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const persistedJobDetailsReducer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistReducer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__persistReducer$3e$__["persistReducer"])(jobDetailsPersistConfig, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$jobDetailsSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const persistedAllSkillsReducer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistReducer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__persistReducer$3e$__["persistReducer"])(allSkillsPersistConfig, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$allSkillsSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const persistedAuthReducer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistReducer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__persistReducer$3e$__["persistReducer"])(authPersistConfig, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const persistedJobRequirementReducer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistReducer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__persistReducer$3e$__["persistReducer"])(jobRequirementPersistConfig, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$jobRequirementSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const persistedInterviewReducer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistReducer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__persistReducer$3e$__["persistReducer"])(interviewPersistConfig, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$interviewSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const store = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["configureStore"])({
    reducer: {
        jobSkills: persistedJobSkillsReducer,
        jobDetails: persistedJobDetailsReducer,
        allSkills: persistedAllSkillsReducer,
        auth: persistedAuthReducer,
        jobRequirement: persistedJobRequirementReducer,
        interview: persistedInterviewReducer
    },
    middleware: (getDefaultMiddleware)=>getDefaultMiddleware({
            serializableCheck: {
                ignoredActions: [
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FLUSH"],
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["REHYDRATE"],
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PAUSE"],
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PERSIST"],
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PURGE"],
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["REGISTER"]
                ]
            }
        })
});
const persistor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistStore$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__persistStore$3e$__["persistStore"])(store);
}}),
"[project]/src/redux/ReduxProvider.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$integration$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/es/integration/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/store.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
const ReduxProvider = ({ children })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Provider"], {
        store: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["store"],
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$integration$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PersistGate"], {
            persistor: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["persistor"],
            children: children
        }, void 0, false, {
            fileName: "[project]/src/redux/ReduxProvider.tsx",
            lineNumber: 14,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/redux/ReduxProvider.tsx",
        lineNumber: 13,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = ReduxProvider;
}}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/utils/syncReduxToCookies.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "syncReduxStateToCookies": (()=>syncReduxStateToCookies)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/commonConstants.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/store.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/js-cookie/dist/js.cookie.mjs [app-ssr] (ecmascript)");
;
;
;
const syncReduxStateToCookies = (permissions, forceSync = false)=>{
    try {
        const permissionData = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PERMISSIONS_COOKIES_KEY"]);
        if (!forceSync && permissionData) {
            return;
        }
        const state = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["store"].getState();
        // Sync auth state to cookies (permissions are in auth state)
        if (state.auth) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].set(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PERMISSIONS_COOKIES_KEY"], JSON.stringify(permissions?.length ? permissions : state.auth.permissions), {
                expires: 4,
                path: "/",
                sameSite: "strict"
            });
        }
        console.log("Redux state synced to cookies");
    } catch (error) {
        console.error("Error syncing Redux state to cookies:", error);
    }
};
}}),
"[project]/public/assets/images/logo.svg (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/logo.cf3f1dcc.svg");}}),
"[project]/public/assets/images/logo.svg.mjs { IMAGE => \"[project]/public/assets/images/logo.svg (static in ecmascript)\" } [app-ssr] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$logo$2e$svg__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/public/assets/images/logo.svg (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$logo$2e$svg__$28$static__in__ecmascript$29$__["default"],
    width: 257,
    height: 70,
    blurDataURL: null,
    blurWidth: 0,
    blurHeight: 0
};
}}),
"[project]/public/assets/images/down-arrow.svg (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/down-arrow.88f69ff5.svg");}}),
"[project]/public/assets/images/down-arrow.svg.mjs { IMAGE => \"[project]/public/assets/images/down-arrow.svg (static in ecmascript)\" } [app-ssr] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$down$2d$arrow$2e$svg__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/public/assets/images/down-arrow.svg (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$down$2d$arrow$2e$svg__$28$static__in__ecmascript$29$__["default"],
    width: 15,
    height: 8,
    blurDataURL: null,
    blurWidth: 0,
    blurHeight: 0
};
}}),
"[project]/public/assets/images/user.png (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/user.8774be1d.png");}}),
"[project]/public/assets/images/user.png.mjs { IMAGE => \"[project]/public/assets/images/user.png (static in ecmascript)\" } [app-ssr] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$user$2e$png__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/public/assets/images/user.png (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$user$2e$png__$28$static__in__ecmascript$29$__["default"],
    width: 51,
    height: 50,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAABE0lEQVR42gEIAff+AAICAgEzMzM5iIiHuaqckfWjl472i4qKvDU1NT4CAgICAC8vLzSXl5fQq6ai/tCpjv/HpIr/q6el/piYmNYyMjI5AH9/f6qsrKz+rqqn/82hiP/UqIz/sKuo/6ysrP6FhYWxAKWlpeaoqav/k5ah/6SIfP+wkH//kZSf/6eoq/+pqansAKSkpeWCip3/UGGI/25sfP93cn//U2KJ/4CInf+np6fsAHt8fqhlcpH+SlqC/0xbgv9HWID/SluF/2hzkf5/gIKyACUnKzNLWXrQc3KC/qWWj/+Cf4z/UmCG/lZhfdYsLS86AAEBAgEWGyc6P0ZduZ2BcPWQfHH2PUdhvRkdJz4BAQICbTiKQnEKIQsAAAAASUVORK5CYII=",
    blurWidth: 8,
    blurHeight: 8
};
}}),
"[project]/src/styles/header.module.scss.module.css [app-ssr] (css module)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "active": "header-module-scss-module__4lyeFq__active",
  "admin_info": "header-module-scss-module__4lyeFq__admin_info",
  "align_header_search": "header-module-scss-module__4lyeFq__align_header_search",
  "circle_img": "header-module-scss-module__4lyeFq__circle_img",
  "dropdown_menu": "header-module-scss-module__4lyeFq__dropdown_menu",
  "header": "header-module-scss-module__4lyeFq__header",
  "header_buttons": "header-module-scss-module__4lyeFq__header_buttons",
  "header_right": "header-module-scss-module__4lyeFq__header_right",
  "hidden": "header-module-scss-module__4lyeFq__hidden",
  "logo": "header-module-scss-module__4lyeFq__logo",
  "navbar_content": "header-module-scss-module__4lyeFq__navbar_content",
  "navbar_links": "header-module-scss-module__4lyeFq__navbar_links",
  "open": "header-module-scss-module__4lyeFq__open",
  "searchBar": "header-module-scss-module__4lyeFq__searchBar",
  "searchButton": "header-module-scss-module__4lyeFq__searchButton",
  "searchContainer": "header-module-scss-module__4lyeFq__searchContainer",
  "searchIcon": "header-module-scss-module__4lyeFq__searchIcon",
  "searchInput": "header-module-scss-module__4lyeFq__searchInput",
  "search_wrapper": "header-module-scss-module__4lyeFq__search_wrapper",
  "show": "header-module-scss-module__4lyeFq__show",
  "user_drop": "header-module-scss-module__4lyeFq__user_drop",
  "user_drop_btn": "header-module-scss-module__4lyeFq__user_drop_btn",
});
}}),
"[project]/src/components/svgComponents/Notification.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
function NotificationIcon(props) {
    const { hasNotification, ...restProps } = props;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        ...restProps,
        className: "cursor-pointer",
        xmlns: "http://www.w3.org/2000/svg",
        width: "25",
        height: "24",
        viewBox: "0 0 33 32",
        fill: "none",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M27.458 22.9624C26.251 20.8511 25.6133 18.4492 25.6133 16.0166C25.6133 16.0166 25.6133 14.0014 25.6133 14C25.6133 10.1198 22.9443 6.54149 19.2454 5.40924C19.4725 4.98712 19.6133 4.51202 19.6133 4C19.6133 2.3457 18.2676 1 16.6133 1C14.959 1 13.6133 2.3457 13.6133 4C13.6133 4.51233 13.7544 4.98767 13.9817 5.40997C10.2878 6.57581 7.61332 10.1457 7.61332 14.3071V16.0166C7.61332 18.4492 6.97562 20.8511 5.76811 22.9629C5.1221 24.0927 4.75006 25.2737 5.46489 26.5054C6.00736 27.4414 6.97758 28 8.05961 28H12.6133C12.6133 30.2056 14.4077 32 16.6133 32C18.8189 32 20.6133 30.2056 20.6133 28H25.167C26.249 28 27.2193 27.4414 27.7617 26.5054C28.4522 25.3141 28.0953 24.0784 27.458 22.9624ZM16.6133 3C17.1646 3 17.6133 3.44873 17.6133 4C17.6133 4.55127 17.1646 5 16.6133 5C16.062 5 15.6133 4.55127 15.6133 4C15.6133 3.44873 16.062 3 16.6133 3ZM16.6133 30C15.5103 30 14.6133 29.103 14.6133 28H18.6133C18.6133 29.103 17.7163 30 16.6133 30ZM26.0323 25.5019C25.9453 25.6514 25.687 26 25.167 26H8.05961C7.53967 26 7.28136 25.6515 7.19441 25.502C6.87823 24.9586 7.23496 24.428 7.50492 23.9546C8.88432 21.542 9.61332 18.7969 9.61332 16.0166C9.61332 16.0166 9.61332 14.3081 9.61332 14.3071C9.61332 10.5303 12.7077 7.00054 16.602 7.00049C20.3752 7.00044 23.6133 10.2392 23.6133 14V16.0166C23.6133 18.7968 24.3423 21.5419 25.7212 23.954C26.0017 24.4448 26.3567 24.9391 26.0323 25.5019Z",
                fill: "#333333"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/Notification.tsx",
                lineNumber: 12,
                columnNumber: 7
            }, this),
            hasNotification && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                cx: "24.6136",
                cy: "10.6654",
                r: "4.83333",
                fill: "#D4000D",
                stroke: "white"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/Notification.tsx",
                lineNumber: 16,
                columnNumber: 27
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgComponents/Notification.tsx",
        lineNumber: 11,
        columnNumber: 5
    }, this);
}
const __TURBOPACK__default__export__ = NotificationIcon;
}}),
"[project]/src/utils/storage.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$secure$2d$ls$2f$dist$2f$secure$2d$ls$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/secure-ls/dist/secure-ls.js [app-ssr] (ecmascript)");
;
const ls = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$secure$2d$ls$2f$dist$2f$secure$2d$ls$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
const storage = {
    set: (key, data)=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    },
    get: (key)=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        return null;
    },
    remove: (key)=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    },
    removeAll: ()=>{
        ls.removeAll();
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    },
    getAllKeys: ()=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        return [];
    }
};
const __TURBOPACK__default__export__ = storage;
}}),
"[project]/src/constants/endpoint.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
// import config from "@/config/config";
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const URL = ("TURBOPACK compile-time value", "http://localhost:3001/api/v1");
const endpoint = {
    auth: {
        SIGNIN: `${URL}/auth/sign-in`,
        VERIFY_OTP: `${URL}/auth/verify-otp`,
        RESEND_OTP: `${URL}/auth/resend-otp`,
        FORGOT_PASSWORD: `${URL}/auth/forgot-password`,
        RESET_PASSWORD: `${URL}/auth/reset-password`,
        DELETE_SESSION: `${URL}/auth/delete-session`,
        UPDATE_TIMEZONE: `${URL}/auth/update-timezone`
    },
    interview: {
        UPDATE_OR_SCHEDULE_INTERVIEW: `${URL}/interview/update-or-schedule-interview`,
        GET_INTERVIEWS: `${URL}/interview/get-interviews`,
        GET_INTERVIEWERS: `${URL}/interview/get-interviewers`,
        GET_MY_INTERVIEWS: `${URL}/interview/get-my-interviews`,
        UPDATE_INTERVIEW_ANSWERS: `${URL}/interview/update-interview-answers`,
        GET_UPCOMING_OR_PAST_INTERVIEW: `${URL}/interview/get-upcoming-or-past-interviews`,
        GET_INTERVIEW_SKILL_QUESTIONS: `${URL}/interview/get-interview-skill-questions`,
        UPDATE_INTERVIEW_SKILL_QUESTION: `${URL}/interview/update-interview-skill-question`,
        ADD_INTERVIEW_SKILL_QUESTION: `${URL}/interview/add-interview-skill-question`,
        GET_COMPLETED_SKILLS: `${URL}/interview/get-completed-skills`,
        GET_JOB_LIST: `${URL}/interview/get-job-list`,
        GET_CANDIDATE_LIST: `${URL}/interview/get-candidate-list`,
        END_INTERVIEW: `${URL}/interview/end-interview`,
        CONDUCT_INTERVIEW_STATIC_INFORMATION: `${URL}/interview/conduct-interview-static-information`
    },
    common: {
        REMOVE_ATTACHMENTS_FROM_S3: `${URL}/remove-attachments-from-s3`,
        GENERATE_PRESIGNED_URL: `${URL}/generate-presignedurl`
    },
    jobRequirements: {
        GENERATE_SKILL: `${URL}/jobs/generate-skills`,
        UPLOAD_URL: `${URL}/jobs/upload-url`,
        PARSE_PDF: `${URL}/jobs/parse-pdf`,
        GET_ALL_SKILLS: `${URL}/jobs/get-all-skills`,
        GENERATE_JOB_REQUIREMENT: `${URL}/jobs/generate-job-requirement`,
        SAVE_JOB_DETAILS: `${URL}/jobs/save-job-details`,
        GET_JOBS_META: `${URL}/jobs/get-jobs-meta`,
        UPDATE_JOB_STATUS: "/jobs/updateJob",
        GET_JOB_HTML_DESCRIPTION: `${URL}/jobs/get-job-html-description`,
        UPDATE_JOB_DESCRIPTION: `${URL}/jobs/update-job-description`,
        GENERATE_PDF: `${URL}/jobs/generate-pdf`
    },
    Dashboard: {
        GET_DASHBOARD_COUNTS: `${URL}/jobs/dashboard-counts`
    },
    resumeScreen: {
        MANUAL_CANDIDATE_UPLOAD: `${URL}/resume-screen/manual-candidate-upload`,
        GET_PRESIGNED_URL: `${URL}/resume-screen/get-presigned-url`,
        GET_ALL_PENDING_JOB_APPLICATIONS: `${URL}/resume-screen/get-all-pending-job-applications`,
        CHANGE_APPLICATION_STATUS: `${URL}/resume-screen/change-application-status`
    },
    employee: {
        ADD_EMPLOYEES: `${URL}/employee-management/add-hiring-employee`,
        GET_EMPLOYEES: `${URL}/employee-management`,
        GET_EMPLOYEES_BY_DEPARTMENT: `${URL}/employee-management/employees`,
        UPDATE_EMPLOYEE_ROLE: `${URL}/employee-management/employee/:employeeId/role`,
        UPDATE_EMPLOYEE_INTERVIEW_ORDER: `${URL}/employee-management/employee/:employeeId/interview-order`
    },
    userprofile: {
        GET_MY_PROFILE: `${URL}/user-profile/get-my-profile`,
        UPDATE_MY_PROFILE: `${URL}/user-profile/update-my-profile`
    },
    roles: {
        GET_ROLES: `${URL}/access-management/user-roles`,
        ADD_USER_ROLE: `${URL}/access-management/add-user-role`,
        UPDATE_USER_ROLE: `${URL}/access-management/user-role`,
        DELETE_USER_ROLE: `${URL}/access-management/user-role`,
        GET_ROLE_PERMISSIONS: `${URL}/access-management/role-permissions`,
        GET_ROLE_PERMISSIONS_BY_ID: `${URL}/access-management/role-permissions/:roleId`,
        UPDATE_ROLE_PERMISSIONS: `${URL}/access-management/role-permissions/:roleId`,
        USER_PERMISSIONS: `${URL}/access-management/user-permissions`
    },
    departments: {
        GET_DEPARTMENTS: `${URL}/employee-management/departments`,
        ADD_DEPARTMENT: `${URL}/employee-management/add-department`,
        UPDATE_DEPARTMENT: `${URL}/employee-management/update-department/:departmentId`,
        DELETE_DEPARTMENT: `${URL}/employee-management/delete-department/:departmentId`
    },
    assessment: {
        CREATE_FINAL_ASSESSMENT: `${URL}/final-assessment/create-final-assessment`,
        GET_FINAL_ASSESSMENT_QUESTIONS: `${URL}/final-assessment/assessment/questions`,
        CREATE_ASSESSMENT_QUESTION: `${URL}/final-assessment/assessment/create-question`,
        SUBMIT_CANDIDATE_ANSWERS: `${URL}/final-assessment/candidate/:candidateId/submit`,
        SHARE_ASSESSMENT: `${URL}/final-assessment/assessment/share`,
        GET_FINAL_ASSESSMENT_BY_CANDIDATE: `${URL}/final-assessment/candidate/assessment`,
        SUBMIT_ASSESSMENT: `${URL}/final-assessment/candidate/assessment/submit`,
        GET_ASSESSMENT_STATUS: `${URL}/final-assessment/assessment-status`,
        VERIFY_CANDIDATE_EMAIL: `${URL}/final-assessment/candidate/verify-email`,
        GENERATE_ASSESSMENT_TOKEN: `${URL}/final-assessment/assessment/generate-token`
    },
    candidatesApplication: {
        ADDITIONAL_INFO: `${URL}/candidates/add-applicant-additional-info`,
        PROMOTE_DEMOTE_CANDIDATE: `${URL}/candidates/update-candidate-rank-status`,
        GET_TOP_CANDIDATES_WITH_APPLICATIONS: `${URL}/candidates/top-candidates`,
        GET_CANDIDATES_WITH_APPLICATIONS: `${URL}/candidates/get-candidates`,
        ARCHIVE_ACTIVE_APPLICATION: `${URL}/candidates/archive-active-application/:applicationId`,
        GET_CANDIDATE_DETAILS: `${URL}/candidates/get-candidate-details`,
        UPDATE_JOB_APPLICATION_STATUS: `${URL}/candidates/update-job-application-status/:jobApplicationId`
    }
};
const __TURBOPACK__default__export__ = endpoint;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[project]/src/config/config.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const config = {
    env: ("TURBOPACK compile-time value", "development"),
    apiBaseUrl: ("TURBOPACK compile-time value", "http://localhost:3001/api/v1")
};
const __TURBOPACK__default__export__ = config;
}}),
"[project]/src/utils/api.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "isObject": (()=>isObject),
    "withData": (()=>withData),
    "withError": (()=>withError)
});
const { toString } = Object.prototype;
const isObject = (arg)=>toString.call(arg) === "[object Object]";
const withError = (arg)=>{
    if (isObject(arg)) {
        return {
            data: null,
            error: {
                ...arg
            }
        };
    }
    return {
        data: null,
        error: {
            message: arg
        }
    };
};
const withData = (data)=>({
        error: null,
        data
    });
}}),
"[project]/src/utils/helper.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "clearStorage": (()=>clearStorage),
    "formatDate": (()=>formatDate),
    "formatTimeForInput": (()=>formatTimeForInput),
    "getAccessToken": (()=>getAccessToken),
    "logout": (()=>logout),
    "pushFileToS3": (()=>pushFileToS3),
    "setAccessToken": (()=>setAccessToken),
    "toTitleCase": (()=>toTitleCase),
    "toastMessageError": (()=>toastMessageError),
    "toastMessageSuccess": (()=>toastMessageSuccess),
    "toastMessageWithIcon": (()=>toastMessageWithIcon),
    "uploadFileOnS3": (()=>uploadFileOnS3)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/react/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/js-cookie/dist/js.cookie.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hot-toast/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$storage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/storage.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/commonConstants.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$authServices$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/authServices.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$commonService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/commonService.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
const getAccessToken = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$storage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ACCESS_TOKEN_KEY"]);
};
const clearStorage = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$storage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].removeAll();
};
const setAccessToken = (accessToken)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$storage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].set(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ACCESS_TOKEN_KEY"], accessToken);
};
/**
 * Toast style object
 */ const style = {
    fontSize: "16px"
};
const toastMessageSuccess = (message)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].success(message, {
        style
    });
};
const toastMessageWithIcon = (message, icon)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].success(message, {
        style,
        icon
    });
};
const toastMessageError = (message)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].error(message, {
        style
    });
};
const logout = async (userId)=>{
    try {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$authServices$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["deleteSession"])(userId);
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["signOut"])({
            redirect: false
        });
        clearStorage();
        // Delete permissions_data cookies when user logs out
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].remove(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PERMISSIONS_COOKIES_KEY"], {
            path: "/"
        });
    } catch (error) {
        console.error("Error in logout:", error);
    }
};
const uploadFileOnS3 = async (file, filePath)=>{
    let body = {
        filePath: "",
        fileFormat: ""
    };
    body = {
        filePath,
        fileFormat: file.type
    };
    let signedUrl;
    const presignedUrl = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$commonService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getSignedUrl"])(body);
    if (presignedUrl && presignedUrl.data) {
        const response = await pushFileToS3(presignedUrl.data.data, file);
        if (response?.url) {
            signedUrl = response?.url.split("?")?.[0];
        }
    }
    return signedUrl?.replace(`${process.env.NEXT_PUBLIC_S3_URL}`, `${process.env.NEXT_PUBLIC_S3_CDN_URL}`);
};
const pushFileToS3 = async (signedUrl, file)=>{
    return fetch(signedUrl, {
        method: "PUT",
        body: file,
        headers: {
            "Content-Type": file.type
        }
    });
};
const formatDate = (dateString)=>{
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric"
    });
};
const formatTimeForInput = (date)=>{
    const hours = date.getHours().toString().padStart(2, "0");
    const minutes = date.getMinutes().toString().padStart(2, "0");
    return `${hours}:${minutes}`;
};
const toTitleCase = (name)=>{
    if (!name) return "";
    return name.toLowerCase().split(" ").filter((word)=>word) // remove extra spaces
    .map((word)=>word[0].toUpperCase() + word.slice(1)).join(" ");
};
}}),
"[project]/src/utils/http.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
// src/utils/http.ts
__turbopack_context__.s({
    "get": (()=>get),
    "http": (()=>http),
    "patch": (()=>patch),
    "post": (()=>post),
    "postFile": (()=>postFile),
    "put": (()=>put),
    "remove": (()=>remove)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/react/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/config/config.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/api.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/helper.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/commonConstants.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
const http = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].apiBaseUrl,
    headers: {
        "Content-Type": "application/json"
    }
});
http.interceptors.request.use(async (req)=>{
    const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getSession"])();
    const accessToken = session?.user?.data?.token;
    if (accessToken) {
        req.headers.authorization = `Bearer ${accessToken}`;
    }
    req.headers["ngrok-skip-browser-warning"] = "fjdlkghjsk";
    return req;
});
// Flag to prevent multiple logout calls
let isLoggingOut = false;
http.interceptors.response.use((res)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["withData"])(res.data), async (err)=>{
    const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getSession"])();
    const userId = session?.user?.data?.authData?.userData?.id;
    const accessToken = session?.user?.data?.token;
    if (err?.response?.status === 401 && !isLoggingOut && accessToken) {
        isLoggingOut = true;
        try {
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logout"])(userId);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastMessageError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TOKEN_EXPIRED"]);
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
        } catch (error) {
            console.error("Session cleanup error:", error);
        } finally{
            isLoggingOut = false;
        }
    } else if (err?.response?.status === 403) {
        // Show toast message for forbidden access (403)
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastMessageError"])(err?.response?.data?.message);
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["withError"])(err?.response?.data?.error);
});
function get(url, params) {
    return http({
        method: "get",
        url,
        params
    });
}
function post(url, data, params) {
    return http({
        method: "post",
        url,
        data,
        params
    });
}
function postFile(url, data, params) {
    return http({
        method: "post",
        url,
        data,
        params,
        headers: {
            "Content-Type": "multipart/form-data"
        }
    });
}
function put(url, data, params) {
    return http({
        method: "put",
        url,
        data,
        params
    });
}
function patch(url, data, params) {
    return http({
        method: "patch",
        url,
        data,
        params
    });
}
function remove(url, params) {
    return http({
        method: "delete",
        url,
        params
    });
}
}}),
"[project]/src/services/authServices.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "deleteSession": (()=>deleteSession),
    "forgotPassword": (()=>forgotPassword),
    "getUserPermissions": (()=>getUserPermissions),
    "logIn": (()=>logIn),
    "resendOTP": (()=>resendOTP),
    "resetPassword": (()=>resetPassword),
    "updateTimezone": (()=>updateTimezone),
    "verifyOTP": (()=>verifyOTP)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/endpoint.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/http.ts [app-ssr] (ecmascript)");
;
;
const logIn = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].auth.SIGNIN, data);
};
const verifyOTP = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].auth.VERIFY_OTP, data);
};
const resendOTP = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].auth.RESEND_OTP, data);
};
const forgotPassword = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].auth.FORGOT_PASSWORD, data);
};
const resetPassword = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].auth.RESET_PASSWORD, data);
};
const deleteSession = (userId)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["remove"])(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].auth.DELETE_SESSION}/${userId}`);
};
const updateTimezone = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].auth.UPDATE_TIMEZONE, data);
};
const getUserPermissions = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].roles.USER_PERMISSIONS);
};
}}),
"[project]/src/services/commonService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getSignedUrl": (()=>getSignedUrl),
    "removeAttachmentsFromS3": (()=>removeAttachmentsFromS3)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/http.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/endpoint.ts [app-ssr] (ecmascript)");
;
;
const removeAttachmentsFromS3 = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].common.REMOVE_ATTACHMENTS_FROM_S3, data);
};
const getSignedUrl = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].common.GENERATE_PRESIGNED_URL, data);
};
}}),
"[project]/public/assets/images/Profile.svg (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/Profile.da3a88a8.svg");}}),
"[project]/public/assets/images/Profile.svg.mjs { IMAGE => \"[project]/public/assets/images/Profile.svg (static in ecmascript)\" } [app-ssr] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$Profile$2e$svg__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/public/assets/images/Profile.svg (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$Profile$2e$svg__$28$static__in__ecmascript$29$__["default"],
    width: 24,
    height: 24,
    blurDataURL: null,
    blurWidth: 0,
    blurHeight: 0
};
}}),
"[project]/src/components/svgComponents/dataSecurityIcon.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
function dataSecurityIcon() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "30",
        height: "30",
        viewBox: "0 0 43 42",
        fill: "none",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                cx: "21.5",
                cy: "21",
                r: "21",
                fill: "url(#paint0_linear_9593_1613)"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/dataSecurityIcon.tsx",
                lineNumber: 6,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M21.5091 9.28711H21.4886C18.2484 9.93022 15.0058 10.5721 11.7656 11.2139C11.7927 14.3154 11.8183 17.4181 11.8451 20.5211C11.8835 25.2709 14.6681 29.5713 18.9852 31.5523C19.8209 31.9347 20.6541 32.3187 21.4886 32.7014V32.7102C21.4924 32.709 21.4961 32.7064 21.499 32.7052C21.5015 32.7064 21.5053 32.709 21.5094 32.7102V32.7014C22.3439 32.3187 23.1771 31.935 24.0128 31.5523C28.3298 29.5716 31.1144 25.2709 31.1529 20.5211C31.1797 17.4184 31.2055 14.3154 31.2323 11.2139C27.9919 10.5721 24.7492 9.93022 21.5091 9.28711ZM29.4181 20.6065C29.3856 24.503 27.1019 28.0303 23.5604 29.6558C22.8757 29.9694 22.1919 30.2841 21.5072 30.5974V30.605C21.5043 30.604 21.5015 30.6021 21.4987 30.6012C21.4968 30.6021 21.4939 30.604 21.4911 30.605V30.5974C20.8064 30.2837 20.1226 29.9691 19.4379 29.6558C15.8964 28.0303 13.6127 24.503 13.5802 20.6065C13.5581 18.0621 13.537 15.5171 13.515 12.9727C16.1735 12.4462 18.8326 11.9198 21.4911 11.3924H21.5075C24.166 11.9198 26.8251 12.4462 29.4837 12.9727C29.4616 15.5171 29.4405 18.0621 29.4184 20.6065H29.4181Z",
                fill: "white"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/dataSecurityIcon.tsx",
                lineNumber: 7,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M25.0804 18.5382H24.734V17.2775C24.734 15.5752 23.3538 14.1953 21.6518 14.1953H21.3743C19.672 14.1953 18.2921 15.5752 18.2921 17.2775V18.5382H17.9457C17.6619 18.5382 17.4321 18.768 17.4321 19.0517V24.4369C17.4321 24.7206 17.6619 24.9517 17.9457 24.9517H25.0807C25.3645 24.9517 25.5955 24.7206 25.5955 24.4369V19.0517C25.5955 18.768 25.3645 18.5382 25.0807 18.5382H25.0804ZM21.7965 21.7077V23.3868C21.7965 23.4804 21.7195 23.5576 21.6243 23.5576H21.3996C21.3059 23.5576 21.2287 23.4807 21.2287 23.3868V21.7077C20.9525 21.5961 20.7561 21.3253 20.7561 21.0069C20.7561 20.5949 21.0875 20.2598 21.4982 20.2535C21.5033 20.2522 21.5074 20.2522 21.5124 20.2522C21.9282 20.2522 22.2671 20.5899 22.2671 21.0069C22.2671 21.3253 22.072 21.5961 21.7961 21.7077H21.7965ZM23.7554 18.5382H19.2136V17.1672C19.2136 15.967 20.1855 14.9938 21.3869 14.9938H21.5808C22.7822 14.9938 23.7554 15.967 23.7554 17.1672V18.5382Z",
                fill: "white"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/dataSecurityIcon.tsx",
                lineNumber: 11,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("defs", {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("linearGradient", {
                    id: "paint0_linear_9593_1613",
                    x1: "-2.3",
                    y1: "17.5",
                    x2: "29.5828",
                    y2: "-6.01022",
                    gradientUnits: "userSpaceOnUse",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                            stopColor: "#74A8FF"
                        }, void 0, false, {
                            fileName: "[project]/src/components/svgComponents/dataSecurityIcon.tsx",
                            lineNumber: 17,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                            offset: "0.474301",
                            stopColor: "#AACAFF"
                        }, void 0, false, {
                            fileName: "[project]/src/components/svgComponents/dataSecurityIcon.tsx",
                            lineNumber: 18,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                            offset: "1",
                            stopColor: "#5D86CC"
                        }, void 0, false, {
                            fileName: "[project]/src/components/svgComponents/dataSecurityIcon.tsx",
                            lineNumber: 19,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/svgComponents/dataSecurityIcon.tsx",
                    lineNumber: 16,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/dataSecurityIcon.tsx",
                lineNumber: 15,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgComponents/dataSecurityIcon.tsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
}
const __TURBOPACK__default__export__ = dataSecurityIcon;
}}),
"[project]/src/constants/routes.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BEFORE_LOGIN_ROUTES": (()=>BEFORE_LOGIN_ROUTES),
    "default": (()=>__TURBOPACK__default__export__)
});
const ROUTES = {
    LOGIN: "/login",
    FORGOT_PASSWORD: "/forgot-password",
    VERIFY: "/verify",
    RESET_PASSWORD: "/reset-password",
    CANDIDATE_ASSESSMENT: "/candidate-assessment",
    DASHBOARD: "/dashboard",
    HOME: "/",
    PROFILE: {
        MY_PROFILE: "/my-profile"
    },
    JOBS: {
        CAREER_BASED_SKILLS: "/career-based-skills",
        ROLE_BASED_SKILLS: "/role-based-skills",
        CULTURE_BASED_SKILLS: "/culture-based-skills",
        GENERATE_JOB: "/generate-job",
        EDIT_SKILLS: "/edit-skills",
        HIRING_TYPE: "/hiring-type",
        JOB_EDITOR: "/job-editor",
        ACTIVE_JOBS: "/active-jobs",
        CANDIDATE_PROFILE: "/candidate-profile",
        ARCHIVE: "/archive"
    },
    SCREEN_RESUME: {
        MANUAL_CANDIDATE_UPLOAD: "/manual-upload-resume",
        CANDIDATE_QUALIFICATION: "/candidate-qualification",
        CANDIDATE_LIST: "/candidates-list",
        CANDIDATES: "/candidates"
    },
    INTERVIEW: {
        ADD_CANDIDATE_INFO: "/additional-submission",
        SCHEDULE_INTERVIEW: "/schedule-interview",
        PRE_INTERVIEW_QUESTIONS_OVERVIEW: "/pre-interview-questions-overview",
        INTERVIEW_QUESTION: "/interview-question",
        CALENDAR: "/calendar",
        INTERVIEW_SUMMARY: "/interview-summary"
    },
    ROLE_EMPLOYEES: {
        USER_ROLE: "/user-roles",
        EMPLOYEE_MANAGEMENT: "/employee-management",
        EMPLOYEE_MANAGEMENT_DETAIL: "/employee-management-detail",
        ADD_EMPLOYEE: "/add-employees",
        ADD_DEPARTMENT: "/add-department"
    },
    FINAL_ASSESSMENT: {
        FINAL_ASSESSMENT: "/final-assessment"
    }
};
const BEFORE_LOGIN_ROUTES = [
    ROUTES.LOGIN,
    ROUTES.FORGOT_PASSWORD,
    ROUTES.VERIFY,
    ROUTES.RESET_PASSWORD,
    ROUTES.CANDIDATE_ASSESSMENT
];
const __TURBOPACK__default__export__ = ROUTES;
}}),
"[project]/src/components/loader/Loader.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-ssr] (ecmascript) <locals>");
"use client";
;
;
const Loader = ({ className })=>{
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `spinner-border ${className}`,
        role: "status",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
            className: "visually-hidden",
            children: t("loading")
        }, void 0, false, {
            fileName: "[project]/src/components/loader/Loader.tsx",
            lineNumber: 9,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/loader/Loader.tsx",
        lineNumber: 8,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = Loader;
}}),
"[project]/src/components/formElements/Button.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$loader$2f$Loader$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/loader/Loader.tsx [app-ssr] (ecmascript)");
;
;
const Button = ({ className, type, disabled = false, onClick, children, loading })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
        type: type,
        className: `theme-btn ${className}`,
        onClick: (e)=>{
            if (onClick) {
                onClick(e);
            }
        },
        disabled: disabled,
        "aria-label": "",
        children: [
            children,
            loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$loader$2f$Loader$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/src/components/formElements/Button.tsx",
                lineNumber: 24,
                columnNumber: 16
            }, this) : null
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/formElements/Button.tsx",
        lineNumber: 12,
        columnNumber: 3
    }, this);
const __TURBOPACK__default__export__ = Button;
}}),
"[project]/src/components/header/Header.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$syncReduxToCookies$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/syncReduxToCookies.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$logo$2e$svg$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$assets$2f$images$2f$logo$2e$svg__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/assets/images/logo.svg.mjs { IMAGE => "[project]/public/assets/images/logo.svg (static in ecmascript)" } [app-ssr] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$down$2d$arrow$2e$svg$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$assets$2f$images$2f$down$2d$arrow$2e$svg__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/assets/images/down-arrow.svg.mjs { IMAGE => "[project]/public/assets/images/down-arrow.svg (static in ecmascript)" } [app-ssr] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$user$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$assets$2f$images$2f$user$2e$png__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/assets/images/user.png.mjs { IMAGE => "[project]/public/assets/images/user.png (static in ecmascript)" } [app-ssr] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$header$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/styles/header.module.scss.module.css [app-ssr] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$Notification$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/Notification.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/helper.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/slices/authSlice.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$authServices$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/authServices.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$Profile$2e$svg$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$assets$2f$images$2f$Profile$2e$svg__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/assets/images/Profile.svg.mjs { IMAGE => "[project]/public/assets/images/Profile.svg (static in ecmascript)" } [app-ssr] (structured image object, ecmascript)');
// Interface definitions moved to authServices.ts
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$dataSecurityIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/dataSecurityIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/routes.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Button.tsx [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const Header = ()=>{
    const [dropdown, SetDropdown] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const userProfile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSelector"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["selectProfileData"]);
    const path = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    const dispatch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useDispatch"])();
    const authData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSelector"])((state)=>state.auth.authData);
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])("header");
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])("common");
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    const dropdownRef = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useRef(null);
    const navigate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const [showNotifications, setShowNotifications] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Handle clicks outside of dropdown to close it
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handleClickOutside = (event)=>{
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                SetDropdown(false);
            }
        };
        // Add event listener when dropdown is open
        if (dropdown) {
            document.addEventListener("mousedown", handleClickOutside);
        }
        // Clean up event listener
        return ()=>{
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [
        dropdown
    ]);
    // Toggle dropdown visibility
    const MenuDropdown = ()=>{
        SetDropdown(!dropdown);
    };
    // Function to fetch permissions using the authServices
    const fetchPermissions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        try {
            const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$authServices$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getUserPermissions"])();
            // Only update Redux store when success is true
            if (response.data?.success) {
                dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["setPermissions"])(response.data.data.rolePermissions));
                // Sync Redux state to cookies after updating permissions
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$syncReduxToCookies$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["syncReduxStateToCookies"])(response.data.data.rolePermissions, true);
            } else {
                console.log("Permission fetch unsuccessful:", response.data?.message);
            }
        } catch (error) {
            console.error("Error fetching permissions:", error);
        }
    }, [
        path,
        dispatch
    ]);
    // when someone manually removes localStor
    // useEffect(() => {
    //   logoutUser();
    // }, []);
    // Sync Redux state to cookies after mounting component
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$syncReduxToCookies$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["syncReduxStateToCookies"])();
    }, []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Check if this is first mount or a genuine route change
        fetchPermissions();
    }, [
        path,
        dispatch,
        fetchPermissions
    ]);
    /**
   * Logs out the user if the access token is invalid.
   * If the access token is invalid, it logs out the user and shows a toast message.
   */ // const logoutUser = async () => {
    //   const token = getAccessToken();
    //   if (!token) {
    //     onHandleLogout();
    //     toast.dismiss();
    //     toastMessageError(t("session_expired"));
    //   }
    // };
    const onHandleLogout = async ()=>{
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logout"])(authData?.id);
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$header$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].header,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                    className: "navbar navbar-expand-sm",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "container",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                className: "navbar-brand",
                                href: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].HOME,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$logo$2e$svg$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$assets$2f$images$2f$logo$2e$svg__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
                                    alt: "logo",
                                    width: 640,
                                    height: 320,
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$header$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].logo
                                }, void 0, false, {
                                    fileName: "[project]/src/components/header/Header.tsx",
                                    lineNumber: 132,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/header/Header.tsx",
                                lineNumber: 131,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `collapse navbar-collapse justify-content-end ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$header$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].navbar_content}`,
                                id: "collapsibleNavbar",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                        className: `navbar-nav ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$header$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].navbar_links}`,
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                className: "nav-item",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                    className: `nav-link ${pathname === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].JOBS.GENERATE_JOB || pathname === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].JOBS.CAREER_BASED_SKILLS || pathname === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].JOBS.CULTURE_BASED_SKILLS || pathname === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].JOBS.ROLE_BASED_SKILLS || pathname === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].JOBS.EDIT_SKILLS || pathname === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].JOBS.JOB_EDITOR || pathname === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].JOBS.HIRING_TYPE ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$header$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].active : ""}`,
                                                    href: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].JOBS.HIRING_TYPE,
                                                    children: t("job_requirement_generations")
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                    lineNumber: 140,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/header/Header.tsx",
                                                lineNumber: 139,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                className: "nav-item",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                    className: `nav-link ${pathname === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].JOBS.ACTIVE_JOBS || pathname?.startsWith(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].SCREEN_RESUME.MANUAL_CANDIDATE_UPLOAD) || pathname?.startsWith(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].SCREEN_RESUME.CANDIDATE_QUALIFICATION) ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$header$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].active : ""}`,
                                                    href: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].JOBS.ACTIVE_JOBS,
                                                    children: t("resume_screening")
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                    lineNumber: 148,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/header/Header.tsx",
                                                lineNumber: 147,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                className: "nav-item",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                    className: "nav-link",
                                                    href: "#",
                                                    children: t("conduct_interview")
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                    lineNumber: 156,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/header/Header.tsx",
                                                lineNumber: 155,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                className: "nav-item",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                    className: `nav-link ${pathname === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].DASHBOARD || pathname === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].SCREEN_RESUME.CANDIDATES || pathname === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].JOBS.ARCHIVE ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$header$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].active : ""}`,
                                                    href: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].DASHBOARD,
                                                    children: tCommon("hm_dashboard")
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                    lineNumber: 161,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/header/Header.tsx",
                                                lineNumber: 160,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/header/Header.tsx",
                                        lineNumber: 138,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$header$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].header_right,
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$Notification$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                hasNotification: true,
                                                onClick: ()=>setShowNotifications(!showNotifications)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/header/Header.tsx",
                                                lineNumber: 171,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: `dropdown ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$header$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].user_drop}`,
                                                ref: dropdownRef,
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        type: "button",
                                                        className: `dropdown-toggle ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$header$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].user_drop_btn}`,
                                                        "data-bs-toggle": "dropdown",
                                                        onClick: MenuDropdown,
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$header$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].circle_img}`,
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                    src: userProfile?.image || __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$user$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$assets$2f$images$2f$user$2e$png__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
                                                                    alt: "Profile",
                                                                    width: 100,
                                                                    height: 100
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                                    lineNumber: 175,
                                                                    columnNumber: 23
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/header/Header.tsx",
                                                                lineNumber: 174,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$header$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].admin_info,
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h5", {
                                                                    children: `${userProfile?.first_name}`
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                                    lineNumber: 178,
                                                                    columnNumber: 23
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/header/Header.tsx",
                                                                lineNumber: 177,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$down$2d$arrow$2e$svg$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$assets$2f$images$2f$down$2d$arrow$2e$svg__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
                                                                alt: "downArrow",
                                                                style: {
                                                                    rotate: `${dropdown ? "180deg" : "0deg"}`
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/header/Header.tsx",
                                                                lineNumber: 180,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                        lineNumber: 173,
                                                        columnNumber: 19
                                                    }, this),
                                                    dropdown && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$header$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdown_menu,
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                        src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$Profile$2e$svg$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$assets$2f$images$2f$Profile$2e$svg__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
                                                                        alt: "userPlaceholder"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                                        lineNumber: 185,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        onClick: ()=>{
                                                                            navigate.push(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].PROFILE.MY_PROFILE);
                                                                            SetDropdown(false);
                                                                        },
                                                                        children: t("my_profile")
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                                        lineNumber: 186,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/header/Header.tsx",
                                                                lineNumber: 184,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                                        xmlns: "http://www.w3.org/2000/svg",
                                                                        width: "18",
                                                                        height: "18",
                                                                        viewBox: "0 0 24 24",
                                                                        fill: "none",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                                                d: "M14.9453 1.25C13.5778 1.24998 12.4754 1.24996 11.6085 1.36652C10.7084 1.48754 9.95049 1.74643 9.34858 2.34835C8.82364 2.87328 8.5584 3.51836 8.41917 4.27635C8.28388 5.01291 8.25799 5.9143 8.25196 6.99583C8.24966 7.41003 8.58358 7.74768 8.99779 7.74999C9.412 7.7523 9.74965 7.41838 9.75195 7.00418C9.75804 5.91068 9.78644 5.1356 9.89449 4.54735C9.9986 3.98054 10.1658 3.65246 10.4092 3.40901C10.686 3.13225 11.0746 2.9518 11.8083 2.85315C12.5637 2.75159 13.5648 2.75 15.0002 2.75H16.0002C17.4356 2.75 18.4367 2.75159 19.1921 2.85315C19.9259 2.9518 20.3144 3.13225 20.5912 3.40901C20.868 3.68577 21.0484 4.07435 21.1471 4.80812C21.2486 5.56347 21.2502 6.56459 21.2502 8V16C21.2502 17.4354 21.2486 18.4365 21.1471 19.1919C21.0484 19.9257 20.868 20.3142 20.5912 20.591C20.3144 20.8678 19.9259 21.0482 19.1921 21.1469C18.4367 21.2484 17.4356 21.25 16.0002 21.25H15.0002C13.5648 21.25 12.5637 21.2484 11.8083 21.1469C11.0746 21.0482 10.686 20.8678 10.4092 20.591C10.1658 20.3475 9.9986 20.0195 9.89449 19.4527C9.78644 18.8644 9.75804 18.0893 9.75195 16.9958C9.74965 16.5816 9.412 16.2477 8.99779 16.25C8.58358 16.2523 8.24966 16.59 8.25196 17.0042C8.25799 18.0857 8.28388 18.9871 8.41917 19.7236C8.5584 20.4816 8.82364 21.1267 9.34858 21.6517C9.95049 22.2536 10.7084 22.5125 11.6085 22.6335C12.4754 22.75 13.5778 22.75 14.9453 22.75H16.0551C17.4227 22.75 18.525 22.75 19.392 22.6335C20.2921 22.5125 21.0499 22.2536 21.6519 21.6517C22.2538 21.0497 22.5127 20.2919 22.6337 19.3918C22.7503 18.5248 22.7502 17.4225 22.7502 16.0549V7.94513C22.7502 6.57754 22.7503 5.47522 22.6337 4.60825C22.5127 3.70814 22.2538 2.95027 21.6519 2.34835C21.0499 1.74643 20.2921 1.48754 19.392 1.36652C18.525 1.24996 17.4227 1.24998 16.0551 1.25H14.9453Z",
                                                                                fill: "#191919"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/header/Header.tsx",
                                                                                lineNumber: 197,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                                                d: "M15 11.25C15.4142 11.25 15.75 11.5858 15.75 12C15.75 12.4142 15.4142 12.75 15 12.75H4.02744L5.98809 14.4306C6.30259 14.7001 6.33901 15.1736 6.06944 15.4881C5.79988 15.8026 5.3264 15.839 5.01191 15.5694L1.51191 12.5694C1.34567 12.427 1.25 12.2189 1.25 12C1.25 11.7811 1.34567 11.573 1.51191 11.4306L5.01191 8.43056C5.3264 8.16099 5.79988 8.19741 6.06944 8.51191C6.33901 8.8264 6.30259 9.29988 5.98809 9.56944L4.02744 11.25H15Z",
                                                                                fill: "#191919"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/header/Header.tsx",
                                                                                lineNumber: 201,
                                                                                columnNumber: 27
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                                        lineNumber: 196,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        onClick: ()=>onHandleLogout(),
                                                                        children: "Logout"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                                        lineNumber: 206,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/header/Header.tsx",
                                                                lineNumber: 195,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                        lineNumber: 183,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/header/Header.tsx",
                                                lineNumber: 172,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/header/Header.tsx",
                                        lineNumber: 170,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/header/Header.tsx",
                                lineNumber: 137,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/header/Header.tsx",
                        lineNumber: 130,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/header/Header.tsx",
                    lineNumber: 129,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/header/Header.tsx",
                lineNumber: 125,
                columnNumber: 7
            }, this),
            showNotifications ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "notifications",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "header-content",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                children: "Notifications"
                            }, void 0, false, {
                                fileName: "[project]/src/components/header/Header.tsx",
                                lineNumber: 219,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                className: "clear-btn p-0",
                                children: "Clear All"
                            }, void 0, false, {
                                fileName: "[project]/src/components/header/Header.tsx",
                                lineNumber: 220,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/header/Header.tsx",
                        lineNumber: 218,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "read-btns",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                className: "primary-btn",
                                children: "All"
                            }, void 0, false, {
                                fileName: "[project]/src/components/header/Header.tsx",
                                lineNumber: 223,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                className: "grey-btn",
                                children: "Unread"
                            }, void 0, false, {
                                fileName: "[project]/src/components/header/Header.tsx",
                                lineNumber: 224,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/header/Header.tsx",
                        lineNumber: 222,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "notification-wrapper",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "notification-item unread",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                        children: "Upcoming Interview for Operations Admin"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/header/Header.tsx",
                                        lineNumber: 228,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        children: "You have an interview scheduled on May 24, 2025 at 10:00 AM. "
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/header/Header.tsx",
                                        lineNumber: 229,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "time",
                                        children: "May 22, 2025 | 03:36 PM"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/header/Header.tsx",
                                        lineNumber: 230,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/header/Header.tsx",
                                lineNumber: 227,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "notification-item",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                        children: "Upcoming Interview for Operations Admin"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/header/Header.tsx",
                                        lineNumber: 233,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        children: "You have an interview scheduled on May 24, 2025 at 10:00 AM. "
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/header/Header.tsx",
                                        lineNumber: 234,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "time",
                                        children: "May 22, 2025 | 03:36 PM"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/header/Header.tsx",
                                        lineNumber: 235,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/header/Header.tsx",
                                lineNumber: 232,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "notification-item",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                        children: "Upcoming Interview for Operations Admin"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/header/Header.tsx",
                                        lineNumber: 238,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        children: "You have an interview scheduled on May 24, 2025 at 10:00 AM. "
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/header/Header.tsx",
                                        lineNumber: 239,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "time",
                                        children: "May 22, 2025 | 03:36 PM"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/header/Header.tsx",
                                        lineNumber: 240,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/header/Header.tsx",
                                lineNumber: 237,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/header/Header.tsx",
                        lineNumber: 226,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/header/Header.tsx",
                lineNumber: 217,
                columnNumber: 9
            }, this) : null,
            pathname === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].JOBS.GENERATE_JOB && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "information-box",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$dataSecurityIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                        fileName: "[project]/src/components/header/Header.tsx",
                        lineNumber: 249,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: "We prioritize your data’s security. With encryption at every step, your privacy is secure and protected."
                    }, void 0, false, {
                        fileName: "[project]/src/components/header/Header.tsx",
                        lineNumber: 250,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/header/Header.tsx",
                lineNumber: 248,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true);
};
const __TURBOPACK__default__export__ = Header;
}}),
"[project]/src/components/header/HeaderWrapper.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>HeaderWrapper)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/header/Header.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/routes.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
function HeaderWrapper() {
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    console.log("pathname", pathname);
    if (__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BEFORE_LOGIN_ROUTES"].includes(pathname)) {
        return null;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
        fileName: "[project]/src/components/header/HeaderWrapper.tsx",
        lineNumber: 14,
        columnNumber: 10
    }, this);
}
}}),

};

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__31b11356._.js.map