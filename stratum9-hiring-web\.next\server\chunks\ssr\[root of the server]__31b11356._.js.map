{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/redux/slices/jobSkillsSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from \"@reduxjs/toolkit\";\nimport { ISkillData, JobSkillsState } from \"@/interfaces/jobRequirementesInterfaces\";\n\n// Define the initial state using that type\nconst initialState: JobSkillsState = {\n  careerSkills: [],\n  roleSpecificSkills: [],\n  cultureSpecificSkills: [],\n};\n\nexport const jobSkillsSlice = createSlice({\n  name: \"jobSkills\",\n  initialState,\n  reducers: {\n    setSkillsData: (\n      state,\n      action: PayloadAction<{\n        careerSkills?: ISkillData[];\n        roleSpecificSkills?: ISkillData[];\n        cultureSpecificSkills?: ISkillData[];\n      }>\n    ) => {\n      if (action.payload.careerSkills) {\n        state.careerSkills = action.payload.careerSkills;\n      }\n      if (action.payload.roleSpecificSkills) {\n        state.roleSpecificSkills = action.payload.roleSpecificSkills;\n      }\n      if (action.payload.cultureSpecificSkills) {\n        state.cultureSpecificSkills = action.payload.cultureSpecificSkills;\n      }\n    },\n    clearSkillsData: (state) => {\n      state.careerSkills = [];\n      state.roleSpecificSkills = [];\n      state.cultureSpecificSkills = [];\n    },\n  },\n});\n\nexport const { setSkillsData, clearSkillsData } = jobSkillsSlice.actions;\n\n// Simple selectors to use directly with useSelector\nexport const selectJobSkillsState = (state: { jobSkills: JobSkillsState }) => state.jobSkills;\nexport const selectCareerSkills = (state: { jobSkills: JobSkillsState }) => state.jobSkills.careerSkills;\nexport const selectRoleSpecificSkills = (state: { jobSkills: JobSkillsState }) => state.jobSkills.roleSpecificSkills;\nexport const selectCultureSpecificSkills = (state: { jobSkills: JobSkillsState }) => state.jobSkills.cultureSpecificSkills;\n\n// Export the reducer directly for easier import in the store\nexport default jobSkillsSlice.reducer;\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAGA,2CAA2C;AAC3C,MAAM,eAA+B;IACnC,cAAc,EAAE;IAChB,oBAAoB,EAAE;IACtB,uBAAuB,EAAE;AAC3B;AAEO,MAAM,iBAAiB,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IACxC,MAAM;IACN;IACA,UAAU;QACR,eAAe,CACb,OACA;YAMA,IAAI,OAAO,OAAO,CAAC,YAAY,EAAE;gBAC/B,MAAM,YAAY,GAAG,OAAO,OAAO,CAAC,YAAY;YAClD;YACA,IAAI,OAAO,OAAO,CAAC,kBAAkB,EAAE;gBACrC,MAAM,kBAAkB,GAAG,OAAO,OAAO,CAAC,kBAAkB;YAC9D;YACA,IAAI,OAAO,OAAO,CAAC,qBAAqB,EAAE;gBACxC,MAAM,qBAAqB,GAAG,OAAO,OAAO,CAAC,qBAAqB;YACpE;QACF;QACA,iBAAiB,CAAC;YAChB,MAAM,YAAY,GAAG,EAAE;YACvB,MAAM,kBAAkB,GAAG,EAAE;YAC7B,MAAM,qBAAqB,GAAG,EAAE;QAClC;IACF;AACF;AAEO,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,GAAG,eAAe,OAAO;AAGjE,MAAM,uBAAuB,CAAC,QAAyC,MAAM,SAAS;AACtF,MAAM,qBAAqB,CAAC,QAAyC,MAAM,SAAS,CAAC,YAAY;AACjG,MAAM,2BAA2B,CAAC,QAAyC,MAAM,SAAS,CAAC,kBAAkB;AAC7G,MAAM,8BAA8B,CAAC,QAAyC,MAAM,SAAS,CAAC,qBAAqB;uCAG3G,eAAe,OAAO", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/constants/commonConstants.ts"], "sourcesContent": ["import { ExtendedFormValues } from \"@/types/types\";\n\nexport const ACCESS_TOKEN_KEY = \"__ATK__\";\n\nexport const EMAIL_REGEX = /^[\\w-]+(\\.[\\w-]+)*@([\\w-]+\\.)+[a-zA-Z]{2,7}$/;\n\nexport const PASSWORD_REGEX = /^(?=.*\\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[^a-zA-Z0-9])(?!.*\\s).{8,16}$/;\n\nexport const MAX_IMAGE_SIZE = 5242880;\n\nexport const ScheduleInterviewformSubmissionType = {\n  SCHEDULE: \"schedule\",\n  UPDATE: \"update\",\n};\nexport const S3_PATHS = {\n  PROFILE_IMAGE: \"profile-images/:path\",\n};\n\nexport const ONE_TO_ONE_INTERVIEW_INSTRUCTIONS = [\n  \"Arrive at the interview location on time with a government-issued ID.\",\n  \"Ensure your phone is on silent mode and distractions are minimized.\",\n  \"Bring a printed copy of your resume and any supporting documents.\",\n  \"Dress professionally and maintain proper body language.\",\n  \"Listen carefully, answer honestly, and ask for clarification if needed.\",\n  \"Respect the interview flow and do not interrupt the interviewer.\",\n  \"Take brief notes if necessary, but focus on active conversation.\",\n  \"If you need assistance or face any issues, notify the interview coordinator.\",\n];\n\nexport const VIDEO_CALL_INTERVIEW_INSTRUCTIONS = [\n  \"Join the interview on time using the link provided.\",\n  \"Ensure a stable internet connection and a quiet, well-lit space.\",\n  \"Test your camera, microphone, and audio settings in advance.\",\n  \"Keep your video on unless instructed otherwise by the interviewer.\",\n  \"Minimize background noise and avoid multitasking during the session.\",\n  \"Use headphones if possible for better audio clarity.\",\n  \"Be attentive, respond clearly, and maintain professional posture.\",\n  \"Contact support if you face technical difficulties before or during the interview.\",\n];\n\n/**\n * Permission Constants\n */\nexport const PERMISSION = {\n  CREATE_OR_EDIT_JOB_POST: \"create-or-edit-job-post\",\n  SCHEDULE_CONDUCT_INTERVIEWS: \"schedule-conduct-interviews\",\n  VIEW_HIRED_CANDIDATES: \"view-hired-candidates\",\n  ARCHIVE_RESTORE_CANDIDATES: \"archive-restore-candidates\",\n  ARCHIVE_RESTORE_JOB_POSTS: \"archive-restore-job-posts\",\n  MANUAL_RESUME_SCREENING: \"manual-resume-screening\",\n  EDIT_SCHEDULED_INTERVIEWS: \"edit-scheduled-interviews\",\n  ADD_ADDITIONAL_CANDIDATE_INFO: \"add-additional-candidate-info\",\n  ADD_OR_EDIT_INTERVIEW_NOTES: \"add-or-edit-interview-notes\",\n  MANAGE_TOP_CANDIDATES: \"manage-top-candidates\",\n  MANAGE_PRE_INTERVIEW_QUESTIONS: \"manage-pre-interview-questions\",\n  CREATE_FINAL_ASSESSMENT: \"create-final-assessment\",\n  VIEW_FINAL_ASSESSMENT: \"view-final-assessment\",\n  VIEW_CANDIDATE_PROFILE_SUMMARY: \"view-candidate-profile-summary\",\n  HIRE_CANDIDATE: \"hire-candidate\",\n  CREATE_NEW_ROLE: \"create-new-role\",\n  MANAGE_USER_PERMISSIONS: \"manage-user-permissions\",\n  CREATE_NEW_DEPARTMENT: \"create-new-department\",\n  ADD_INTERVIEW_PARTICIPANTS: \"add-interview-participants\",\n  VIEW_SUBSCRIPTION_PLAN: \"view-subscription-plan\",\n  MANAGE_SUBSCRIPTIONS: \"manage-subscriptions\",\n  VIEW_AUDIT_LOGS_UPCOMING: \"view-audit-logs-upcoming\",\n  VIEW_ALL_SCHEDULED_INTERVIEWS: \"view-all-scheduled-interviews\",\n};\n\n/**\n * Skill Constants\n */\nexport const SKILL_CONSTANTS = {\n  REQUIRED_ROLE_SKILLS: 10,\n  REQUIRED_CULTURE_SKILLS: 5,\n};\nexport const commonConstants = {\n  finalAssessmentId: \"finalAssessmentId\",\n  token: \"token\",\n  isShared: \"isShared\",\n  isSubmitted: \"isSubmitted\",\n  jobId: \"jobId\",\n  jobApplicationId: \"jobApplicationId\",\n};\n\nexport const QuestionType = {\n  MCQ: \"mcq\",\n  TRUE_FALSE: \"true_false\",\n};\n\n// Constants for option IDs\nexport const OPTION_ID = {\n  A: \"A\",\n  B: \"B\",\n  C: \"C\",\n  D: \"D\",\n  TRUE: \"true\",\n  FALSE: \"false\",\n} as const;\n\n// Constants for question types\nexport const QUESTION_TYPE = {\n  MCQ: \"mcq\" as const,\n  TRUE_FALSE: \"true_false\" as const,\n};\n\n// Constants for default options\nexport const DEFAULT_MCQ_OPTIONS = [\n  { id: OPTION_ID.A, text: \"\" },\n  { id: OPTION_ID.B, text: \"\" },\n  { id: OPTION_ID.C, text: \"\" },\n  { id: OPTION_ID.D, text: \"\" },\n];\n\nexport const DEFAULT_TRUE_FALSE_OPTIONS = [\n  { id: OPTION_ID.TRUE, text: \"True\" },\n  { id: OPTION_ID.FALSE, text: \"False\" },\n];\n\nexport const INTERVIEW_SCHEDULE_ROUND_TYPE = [\n  {\n    label: \"One-On-One\",\n    value: \"One-On-One\",\n  },\n  {\n    label: \"Video Call\",\n    value: \"Video Call\",\n  },\n];\n\n/**\n * Interview Question Types\n */\nexport const QUESTION_TYPES = {\n  ROLE_SPECIFIC: \"role_specific\",\n  CULTURE_SPECIFIC: \"culture_specific\",\n  CAREER_BASED: \"career_based\",\n} as const;\n\nexport type QuestionType = (typeof QUESTION_TYPES)[keyof typeof QUESTION_TYPES];\n/**\n * Empty Content Patterns\n */\nexport const EMPTY_CONTENT_PATTERNS = [\"<p><br></p>\", \"<p></p>\", \"<div><br></div>\", \"<div></div>\", \"<p>&nbsp;</p>\"];\n\n// Define the initial state using FormValues type\nexport const initialState: ExtendedFormValues = {\n  title: \"\",\n  employment_type: \"\",\n  department_id: \"\",\n  salary_range: \"\",\n  salary_cycle: \"\",\n  location_type: \"\",\n  state: \"\",\n  city: \"\",\n  role_overview: \"\",\n  experience_level: \"\",\n  responsibilities: \"\",\n  educations_requirement: \"\",\n  certifications: undefined,\n  skills_and_software_expertise: \"\",\n  experience_required: \"\",\n  ideal_candidate_traits: \"\",\n  about_company: \"\",\n  perks_benefits: undefined,\n  tone_style: \"\",\n  additional_info: undefined,\n  compliance_statement: [],\n  show_compliance: false,\n  hiring_type: \"\",\n};\n\n// Define the skill item interface\nexport interface ISkillItem {\n  id: number;\n  title: string;\n  description: string;\n  short_description: string;\n}\n\n// Define a skill category interface\nexport interface ISkillCategory {\n  type: string;\n  items: ISkillItem[];\n}\n\n// Define the slice state type\nexport interface AllSkillsState {\n  categories: ISkillCategory[];\n  loading: boolean;\n  error: string | null;\n}\n\nexport const FILE_EXTENSION = [\n  \"pdf\",\n  \"plain\",\n  \"csv\",\n  \"vnd.ms-excel.sheet.macroEnabled.12\",\n  \"vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n  \"vnd.openxmlformats-officedocument.wordprocessingml.document\",\n  \"vnd.openxmlformats-officedocument.presentationml.presentation\",\n];\n\nexport const ACTIVE = \"active\";\nexport const TOKEN_EXPIRED = \"Session Expired! Please log in again.\";\nexport const DEFAULT_LIMIT = 15;\n\nexport const IMAGE_EXTENSIONS = [\"png\", \"jpg\", \"jpeg\", \"gif\", \"webp\"];\n\nexport const ASSESSMENT_INSTRUCTIONS = {\n  instructions: [\n    \"Do not refresh or close the browser\",\n    \"Check your internet connection\",\n    \"Ensure a distraction-free environment\",\n    \"Click 'Submit' only once when finished\",\n    \"Read each question carefully\",\n    \"Manage your time efficiently\",\n    \"Avoid any form of plagiarism\",\n    \"Reach out to support if needed\",\n  ],\n};\nexport const PERMISSIONS_COOKIES_KEY = \"permissions_data\";\n\nexport const PDF_FILE_NAME = \"pdf\";\nexport const PDF_FILE_TYPE = \"application/pdf\";\nexport const PDF_FILE_SIZE_LIMIT = 5 * 1024 * 1024;\nexport const PDF_ADDITIONAL_SUBMISSION_LIMIT = 10854484;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEO,MAAM,mBAAmB;AAEzB,MAAM,cAAc;AAEpB,MAAM,iBAAiB;AAEvB,MAAM,iBAAiB;AAEvB,MAAM,sCAAsC;IACjD,UAAU;IACV,QAAQ;AACV;AACO,MAAM,WAAW;IACtB,eAAe;AACjB;AAEO,MAAM,oCAAoC;IAC/C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,oCAAoC;IAC/C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAKM,MAAM,aAAa;IACxB,yBAAyB;IACzB,6BAA6B;IAC7B,uBAAuB;IACvB,4BAA4B;IAC5B,2BAA2B;IAC3B,yBAAyB;IACzB,2BAA2B;IAC3B,+BAA+B;IAC/B,6BAA6B;IAC7B,uBAAuB;IACvB,gCAAgC;IAChC,yBAAyB;IACzB,uBAAuB;IACvB,gCAAgC;IAChC,gBAAgB;IAChB,iBAAiB;IACjB,yBAAyB;IACzB,uBAAuB;IACvB,4BAA4B;IAC5B,wBAAwB;IACxB,sBAAsB;IACtB,0BAA0B;IAC1B,+BAA+B;AACjC;AAKO,MAAM,kBAAkB;IAC7B,sBAAsB;IACtB,yBAAyB;AAC3B;AACO,MAAM,kBAAkB;IAC7B,mBAAmB;IACnB,OAAO;IACP,UAAU;IACV,aAAa;IACb,OAAO;IACP,kBAAkB;AACpB;AAEO,MAAM,eAAe;IAC1B,KAAK;IACL,YAAY;AACd;AAGO,MAAM,YAAY;IACvB,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,MAAM;IACN,OAAO;AACT;AAGO,MAAM,gBAAgB;IAC3B,KAAK;IACL,YAAY;AACd;AAGO,MAAM,sBAAsB;IACjC;QAAE,IAAI,UAAU,CAAC;QAAE,MAAM;IAAG;IAC5B;QAAE,IAAI,UAAU,CAAC;QAAE,MAAM;IAAG;IAC5B;QAAE,IAAI,UAAU,CAAC;QAAE,MAAM;IAAG;IAC5B;QAAE,IAAI,UAAU,CAAC;QAAE,MAAM;IAAG;CAC7B;AAEM,MAAM,6BAA6B;IACxC;QAAE,IAAI,UAAU,IAAI;QAAE,MAAM;IAAO;IACnC;QAAE,IAAI,UAAU,KAAK;QAAE,MAAM;IAAQ;CACtC;AAEM,MAAM,gCAAgC;IAC3C;QACE,OAAO;QACP,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;IACT;CACD;AAKM,MAAM,iBAAiB;IAC5B,eAAe;IACf,kBAAkB;IAClB,cAAc;AAChB;AAMO,MAAM,yBAAyB;IAAC;IAAe;IAAW;IAAmB;IAAe;CAAgB;AAG5G,MAAM,eAAmC;IAC9C,OAAO;IACP,iBAAiB;IACjB,eAAe;IACf,cAAc;IACd,cAAc;IACd,eAAe;IACf,OAAO;IACP,MAAM;IACN,eAAe;IACf,kBAAkB;IAClB,kBAAkB;IAClB,wBAAwB;IACxB,gBAAgB;IAChB,+BAA+B;IAC/B,qBAAqB;IACrB,wBAAwB;IACxB,eAAe;IACf,gBAAgB;IAChB,YAAY;IACZ,iBAAiB;IACjB,sBAAsB,EAAE;IACxB,iBAAiB;IACjB,aAAa;AACf;AAuBO,MAAM,iBAAiB;IAC5B;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,SAAS;AACf,MAAM,gBAAgB;AACtB,MAAM,gBAAgB;AAEtB,MAAM,mBAAmB;IAAC;IAAO;IAAO;IAAQ;IAAO;CAAO;AAE9D,MAAM,0BAA0B;IACrC,cAAc;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AACO,MAAM,0BAA0B;AAEhC,MAAM,gBAAgB;AACtB,MAAM,gBAAgB;AACtB,MAAM,sBAAsB,IAAI,OAAO;AACvC,MAAM,kCAAkC", "debugId": null}}, {"offset": {"line": 295, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/redux/slices/jobDetailsSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from \"@reduxjs/toolkit\";\nimport { ExtendedFormValues } from \"@/types/types\";\nimport { initialState } from \"@/constants/commonConstants\";\n\nexport const jobDetailsSlice = createSlice({\n  name: \"jobDetails\",\n  initialState,\n  reducers: {\n    // Set all job details at once\n    setJobDetails: (state, action: PayloadAction<ExtendedFormValues>) => {\n      return { ...state, ...action.payload };\n    },\n    // Clear job details\n    clearJobDetails: () => {\n      return initialState;\n    },\n    // Update a specific field in job details\n    updateJobDetail: <T extends keyof ExtendedFormValues>(\n      state: ExtendedFormValues,\n      action: PayloadAction<{ field: T; value: ExtendedFormValues[T] }>\n    ) => {\n      const { field, value } = action.payload;\n      state[field] = value;\n    },\n  },\n});\n\nexport const { setJobDetails, clearJobDetails, updateJobDetail } = jobDetailsSlice.actions;\n\n// Simple selectors to use directly with useSelector\nexport const selectJobDetails = (state: { jobDetails: ExtendedFormValues }) => state.jobDetails;\n\n// Export the reducer directly for easier import in the store\nexport default jobDetailsSlice.reducer;\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAEA;;;AAEO,MAAM,kBAAkB,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IACzC,MAAM;IACN,cAAA,mIAAA,CAAA,eAAY;IACZ,UAAU;QACR,8BAA8B;QAC9B,eAAe,CAAC,OAAO;YACrB,OAAO;gBAAE,GAAG,KAAK;gBAAE,GAAG,OAAO,OAAO;YAAC;QACvC;QACA,oBAAoB;QACpB,iBAAiB;YACf,OAAO,mIAAA,CAAA,eAAY;QACrB;QACA,yCAAyC;QACzC,iBAAiB,CACf,OACA;YAEA,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,OAAO,OAAO;YACvC,KAAK,CAAC,MAAM,GAAG;QACjB;IACF;AACF;AAEO,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,eAAe,EAAE,GAAG,gBAAgB,OAAO;AAGnF,MAAM,mBAAmB,CAAC,QAA8C,MAAM,UAAU;uCAGhF,gBAAgB,OAAO", "debugId": null}}, {"offset": {"line": 338, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 344, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/redux/slices/allSkillsSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from \"@reduxjs/toolkit\";\nimport { RootState } from \"../store\";\nimport { AllSkillsState, ISkillCategory, ISkillItem } from \"@/interfaces/jobRequirementesInterfaces\";\n\n// Define the initial state\nconst initialState: AllSkillsState = {\n  categories: [],\n  loading: false,\n  error: null,\n};\n\n// Create the slice\nexport const allSkillsSlice = createSlice({\n  name: \"allSkills\",\n  initialState,\n  reducers: {\n    fetchSkillsStart: (state: AllSkillsState) => {\n      state.loading = true;\n      state.error = null;\n    },\n    fetchSkillsSuccess: (state: AllSkillsState, action: PayloadAction<ISkillCategory[]>) => {\n      state.categories = action.payload;\n      state.loading = false;\n      state.error = null;\n    },\n    fetchSkillsFailure: (state: AllSkillsState, action: PayloadAction<string>) => {\n      state.loading = false;\n      state.error = action.payload;\n    },\n    updateSkillItem: (\n      state: AllSkillsState,\n      action: PayloadAction<{\n        categoryType: string;\n        skillId: number;\n        updatedSkill: Partial<ISkillItem>;\n      }>\n    ) => {\n      const { categoryType, skillId, updatedSkill } = action.payload;\n      const categoryIndex = state.categories.findIndex((cat) => cat.type === categoryType);\n\n      if (categoryIndex !== -1) {\n        const skillIndex = state.categories[categoryIndex].items.findIndex((item) => item.id === skillId);\n\n        if (skillIndex !== -1) {\n          state.categories[categoryIndex].items[skillIndex] = {\n            ...state.categories[categoryIndex].items[skillIndex],\n            ...updatedSkill,\n          };\n        }\n      }\n    },\n  },\n});\n\n// Export actions\nexport const { fetchSkillsStart, fetchSkillsSuccess, fetchSkillsFailure, updateSkillItem } = allSkillsSlice.actions;\n\n// Define selectors\nexport const selectAllSkills = (state: RootState) => state.allSkills.categories;\nexport const selectSkillsLoading = (state: RootState) => state.allSkills.loading;\nexport const selectSkillsError = (state: RootState) => state.allSkills.error;\n\n// Export specific category selector\nexport const selectSkillsByCategory = (categoryType: string) => (state: RootState) =>\n  state.allSkills.categories.find((cat: ISkillCategory) => cat.type === categoryType)?.items || [];\n\n// Export reducer\nexport default allSkillsSlice.reducer;\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;AAIA,2BAA2B;AAC3B,MAAM,eAA+B;IACnC,YAAY,EAAE;IACd,SAAS;IACT,OAAO;AACT;AAGO,MAAM,iBAAiB,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IACxC,MAAM;IACN;IACA,UAAU;QACR,kBAAkB,CAAC;YACjB,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG;QAChB;QACA,oBAAoB,CAAC,OAAuB;YAC1C,MAAM,UAAU,GAAG,OAAO,OAAO;YACjC,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG;QAChB;QACA,oBAAoB,CAAC,OAAuB;YAC1C,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;QACA,iBAAiB,CACf,OACA;YAMA,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,OAAO,OAAO;YAC9D,MAAM,gBAAgB,MAAM,UAAU,CAAC,SAAS,CAAC,CAAC,MAAQ,IAAI,IAAI,KAAK;YAEvE,IAAI,kBAAkB,CAAC,GAAG;gBACxB,MAAM,aAAa,MAAM,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;gBAEzF,IAAI,eAAe,CAAC,GAAG;oBACrB,MAAM,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,WAAW,GAAG;wBAClD,GAAG,MAAM,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,WAAW;wBACpD,GAAG,YAAY;oBACjB;gBACF;YACF;QACF;IACF;AACF;AAGO,MAAM,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,eAAe,EAAE,GAAG,eAAe,OAAO;AAG5G,MAAM,kBAAkB,CAAC,QAAqB,MAAM,SAAS,CAAC,UAAU;AACxE,MAAM,sBAAsB,CAAC,QAAqB,MAAM,SAAS,CAAC,OAAO;AACzE,MAAM,oBAAoB,CAAC,QAAqB,MAAM,SAAS,CAAC,KAAK;AAGrE,MAAM,yBAAyB,CAAC,eAAyB,CAAC,QAC/D,MAAM,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,MAAwB,IAAI,IAAI,KAAK,eAAe,SAAS,EAAE;uCAGnF,eAAe,OAAO", "debugId": null}}, {"offset": {"line": 402, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 408, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/redux/slices/authSlice.ts"], "sourcesContent": ["import { Permission, Role, IUserData, IDepartment } from \"@/interfaces/authInterfaces\";\nimport { createSlice, PayloadAction } from \"@reduxjs/toolkit\";\n\nexport interface AuthState {\n  authData: IUserData | null;\n  department: IDepartment | null;\n  role: Role | null;\n  permissions: Permission[];\n}\n\nconst initialState: AuthState = {\n  authData: {\n    id: -1,\n    account_type: \"\",\n    email: \"\",\n    isVerified: false,\n    sms_notification: false,\n    allow_notification: false,\n    is_deleted: false,\n    image: \"\",\n    orgId: -1,\n    departmentId: -1,\n    organizationName: \"\",\n    organizationCode: \"\",\n    createdTs: \"\",\n    first_name: \"\",\n    last_name: \"\",\n  },\n  department: null,\n  role: null,\n  permissions: [],\n};\n\nexport const authSlice = createSlice({\n  name: \"auth\",\n  initialState,\n  reducers: {\n    setAuthData: (state, action) => {\n      state.authData = action.payload;\n    },\n    setRole: (state, action) => {\n      state.role = action.payload;\n    },\n    setDepartment: (state, action) => {\n      state.department = action.payload;\n    },\n    setPermissions: (state, action) => {\n      state.permissions = action.payload;\n    },\n    updateUserProfileData: (\n      state,\n      action: PayloadAction<{\n        first_name?: string;\n        last_name?: string;\n        image?: string | null;\n      }>\n    ) => {\n      if (state.authData) {\n        const { first_name, last_name, image } = action.payload;\n\n        // Update firstName and lastName separately\n        if (first_name !== undefined) {\n          state.authData.first_name = first_name;\n        }\n\n        if (last_name !== undefined) {\n          state.authData.last_name = last_name;\n        }\n\n        // Update image if provided\n        if (image !== undefined) {\n          state.authData.image = image;\n        }\n      }\n    },\n  },\n});\n\nexport const selectRole = (state: { auth: AuthState }) => state.auth.role;\nexport const selectDepartment = (state: { auth: AuthState }) => state.auth.department;\nexport const selectPermissions = (state: { auth: AuthState }) => state.auth.permissions;\nexport const selectProfileData = (state: { auth: AuthState }) => state.auth.authData;\n\nexport const { setAuthData, setRole, setDepartment, setPermissions, updateUserProfileData } = authSlice.actions;\n\nexport default authSlice.reducer;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AACA;;AASA,MAAM,eAA0B;IAC9B,UAAU;QACR,IAAI,CAAC;QACL,cAAc;QACd,OAAO;QACP,YAAY;QACZ,kBAAkB;QAClB,oBAAoB;QACpB,YAAY;QACZ,OAAO;QACP,OAAO,CAAC;QACR,cAAc,CAAC;QACf,kBAAkB;QAClB,kBAAkB;QAClB,WAAW;QACX,YAAY;QACZ,WAAW;IACb;IACA,YAAY;IACZ,MAAM;IACN,aAAa,EAAE;AACjB;AAEO,MAAM,YAAY,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IACnC,MAAM;IACN;IACA,UAAU;QACR,aAAa,CAAC,OAAO;YACnB,MAAM,QAAQ,GAAG,OAAO,OAAO;QACjC;QACA,SAAS,CAAC,OAAO;YACf,MAAM,IAAI,GAAG,OAAO,OAAO;QAC7B;QACA,eAAe,CAAC,OAAO;YACrB,MAAM,UAAU,GAAG,OAAO,OAAO;QACnC;QACA,gBAAgB,CAAC,OAAO;YACtB,MAAM,WAAW,GAAG,OAAO,OAAO;QACpC;QACA,uBAAuB,CACrB,OACA;YAMA,IAAI,MAAM,QAAQ,EAAE;gBAClB,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,OAAO;gBAEvD,2CAA2C;gBAC3C,IAAI,eAAe,WAAW;oBAC5B,MAAM,QAAQ,CAAC,UAAU,GAAG;gBAC9B;gBAEA,IAAI,cAAc,WAAW;oBAC3B,MAAM,QAAQ,CAAC,SAAS,GAAG;gBAC7B;gBAEA,2BAA2B;gBAC3B,IAAI,UAAU,WAAW;oBACvB,MAAM,QAAQ,CAAC,KAAK,GAAG;gBACzB;YACF;QACF;IACF;AACF;AAEO,MAAM,aAAa,CAAC,QAA+B,MAAM,IAAI,CAAC,IAAI;AAClE,MAAM,mBAAmB,CAAC,QAA+B,MAAM,IAAI,CAAC,UAAU;AAC9E,MAAM,oBAAoB,CAAC,QAA+B,MAAM,IAAI,CAAC,WAAW;AAChF,MAAM,oBAAoB,CAAC,QAA+B,MAAM,IAAI,CAAC,QAAQ;AAE7E,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,aAAa,EAAE,cAAc,EAAE,qBAAqB,EAAE,GAAG,UAAU,OAAO;uCAEhG,UAAU,OAAO", "debugId": null}}, {"offset": {"line": 485, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 491, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/redux/slices/jobRequirementSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from \"@reduxjs/toolkit\";\nimport { JobRequirementState } from \"@/interfaces/jobRequirementesInterfaces\";\n\nconst initialState: JobRequirementState = {\n  content: \"\",\n  isGenerated: false,\n  generatedAt: null,\n};\n\nexport const jobRequirementSlice = createSlice({\n  name: \"jobRequirement\",\n  initialState,\n  reducers: {\n    // Set job requirement content\n    setJobRequirement: (state, action: PayloadAction<string>) => {\n      state.content = action.payload;\n      state.isGenerated = true;\n      state.generatedAt = new Date().toISOString();\n    },\n    // Clear job requirement data\n    clearJobRequirement: (state) => {\n      state.content = \"\";\n      state.isGenerated = false;\n      state.generatedAt = null;\n    },\n  },\n});\n\nexport const { setJobRequirement, clearJobRequirement } = jobRequirementSlice.actions;\n\n// Selector to use with useSelector\nexport const selectJobRequirement = (state: { jobRequirement: JobRequirementState }) => state.jobRequirement;\n\nexport default jobRequirementSlice.reducer;\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAGA,MAAM,eAAoC;IACxC,SAAS;IACT,aAAa;IACb,aAAa;AACf;AAEO,MAAM,sBAAsB,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IAC7C,MAAM;IACN;IACA,UAAU;QACR,8BAA8B;QAC9B,mBAAmB,CAAC,OAAO;YACzB,MAAM,OAAO,GAAG,OAAO,OAAO;YAC9B,MAAM,WAAW,GAAG;YACpB,MAAM,WAAW,GAAG,IAAI,OAAO,WAAW;QAC5C;QACA,6BAA6B;QAC7B,qBAAqB,CAAC;YACpB,MAAM,OAAO,GAAG;YAChB,MAAM,WAAW,GAAG;YACpB,MAAM,WAAW,GAAG;QACtB;IACF;AACF;AAEO,MAAM,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,GAAG,oBAAoB,OAAO;AAG9E,MAAM,uBAAuB,CAAC,QAAmD,MAAM,cAAc;uCAE7F,oBAAoB,OAAO", "debugId": null}}, {"offset": {"line": 526, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 532, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/redux/slices/interviewSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from \"@reduxjs/toolkit\";\nimport { QUESTION_TYPES, QuestionType } from \"@/constants/commonConstants\";\nimport { IGetInterviewSkillQuestionsResponse, IInterviewStaticInformation } from \"@/interfaces/interviewInterfaces\";\n\nexport interface IQuestionAnswer {\n  questionId: number;\n  answer: string;\n}\n\nexport interface IUpdateQuestionAnswerPayload {\n  questionType: QuestionType;\n  questionAnswers: IQuestionAnswer[];\n  stratumScore: number;\n  category?: string;\n  interviewerName?: string;\n}\n\nconst initialState: IGetInterviewSkillQuestionsResponse & { interviewStaticInformation: IInterviewStaticInformation } = {\n  roleSpecificQuestions: {},\n  cultureSpecificQuestions: {},\n  careerBasedQuestions: {\n    questions: [],\n    score: 0,\n  },\n  interviewStaticInformation: {\n    oneToOneInterviewInstructions: [],\n    videoCallInterviewInstructions: [],\n    startumDescription: [],\n  },\n};\n\nexport const interviewSlice = createSlice({\n  name: \"interview\",\n  initialState,\n  reducers: {\n    setInterviewQuestions: (state, action: PayloadAction<IGetInterviewSkillQuestionsResponse>) => {\n      // Handle role-specific questions\n      if (action.payload.roleSpecificQuestions !== undefined) {\n        state.roleSpecificQuestions = action.payload.roleSpecificQuestions;\n      }\n\n      // Handle culture-specific questions\n      if (action.payload.cultureSpecificQuestions !== undefined) {\n        state.cultureSpecificQuestions = action.payload.cultureSpecificQuestions;\n      }\n\n      // Handle career-based questions\n      if (action.payload.careerBasedQuestions !== undefined) {\n        state.careerBasedQuestions = action.payload.careerBasedQuestions;\n      }\n    },\n\n    setInterviewStaticInformation: (state, action: PayloadAction<IInterviewStaticInformation>) => {\n      state.interviewStaticInformation = action.payload;\n    },\n\n    updateQuestionAnswer: (state, action: PayloadAction<IUpdateQuestionAnswerPayload>) => {\n      const { questionType, category, questionAnswers, stratumScore, interviewerName } = action.payload;\n\n      // Create a Map for O(1) lookups\n      const answerMap = new Map(questionAnswers.map((qa) => [qa.questionId, qa.answer]));\n\n      switch (questionType) {\n        case QUESTION_TYPES.CAREER_BASED:\n          // Update answers\n          state.careerBasedQuestions.questions = state.careerBasedQuestions.questions.map((question) => {\n            const answer = answerMap.get(question.id);\n            if (answer !== undefined) {\n              return { ...question, answer };\n            }\n            return question;\n          });\n\n          // Update score\n          state.careerBasedQuestions.score = stratumScore;\n          break;\n\n        case QUESTION_TYPES.ROLE_SPECIFIC:\n          if (category) {\n            // Initialize category if it doesn't exist\n            if (!state.roleSpecificQuestions[category]) {\n              state.roleSpecificQuestions[category] = {\n                questions: [],\n                score: 0,\n              };\n            }\n\n            // Update answers\n            state.roleSpecificQuestions[category].questions = state.roleSpecificQuestions[category].questions.map((question) => {\n              const answer = answerMap.get(question.id);\n              if (answer !== undefined) {\n                return { ...question, answer };\n              }\n              return question;\n            });\n\n            // Update score and interviewer name\n            state.roleSpecificQuestions[category].score = stratumScore;\n            state.roleSpecificQuestions[category].interviewerName = interviewerName;\n          }\n          break;\n\n        case QUESTION_TYPES.CULTURE_SPECIFIC:\n          if (category) {\n            // Initialize category if it doesn't exist\n            if (!state.cultureSpecificQuestions[category]) {\n              state.cultureSpecificQuestions[category] = {\n                questions: [],\n                score: 0,\n              };\n            }\n\n            // Update answers\n            state.cultureSpecificQuestions[category].questions = state.cultureSpecificQuestions[category].questions.map((question) => {\n              const answer = answerMap.get(question.id);\n              if (answer !== undefined) {\n                return { ...question, answer };\n              }\n              return question;\n            });\n\n            // Update score and interviewer name\n            state.cultureSpecificQuestions[category].score = stratumScore;\n            state.cultureSpecificQuestions[category].interviewerName = interviewerName;\n          }\n          break;\n      }\n    },\n\n    clearInterview: (state) => {\n      // Reset state to initial values\n      state.roleSpecificQuestions = initialState.roleSpecificQuestions;\n      state.cultureSpecificQuestions = initialState.cultureSpecificQuestions;\n      state.careerBasedQuestions = initialState.careerBasedQuestions;\n    },\n  },\n});\n\nexport const { setInterviewQuestions, updateQuestionAnswer, clearInterview, setInterviewStaticInformation } = interviewSlice.actions;\n\nexport default interviewSlice.reducer;\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAgBA,MAAM,eAAkH;IACtH,uBAAuB,CAAC;IACxB,0BAA0B,CAAC;IAC3B,sBAAsB;QACpB,WAAW,EAAE;QACb,OAAO;IACT;IACA,4BAA4B;QAC1B,+BAA+B,EAAE;QACjC,gCAAgC,EAAE;QAClC,oBAAoB,EAAE;IACxB;AACF;AAEO,MAAM,iBAAiB,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IACxC,MAAM;IACN;IACA,UAAU;QACR,uBAAuB,CAAC,OAAO;YAC7B,iCAAiC;YACjC,IAAI,OAAO,OAAO,CAAC,qBAAqB,KAAK,WAAW;gBACtD,MAAM,qBAAqB,GAAG,OAAO,OAAO,CAAC,qBAAqB;YACpE;YAEA,oCAAoC;YACpC,IAAI,OAAO,OAAO,CAAC,wBAAwB,KAAK,WAAW;gBACzD,MAAM,wBAAwB,GAAG,OAAO,OAAO,CAAC,wBAAwB;YAC1E;YAEA,gCAAgC;YAChC,IAAI,OAAO,OAAO,CAAC,oBAAoB,KAAK,WAAW;gBACrD,MAAM,oBAAoB,GAAG,OAAO,OAAO,CAAC,oBAAoB;YAClE;QACF;QAEA,+BAA+B,CAAC,OAAO;YACrC,MAAM,0BAA0B,GAAG,OAAO,OAAO;QACnD;QAEA,sBAAsB,CAAC,OAAO;YAC5B,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,eAAe,EAAE,YAAY,EAAE,eAAe,EAAE,GAAG,OAAO,OAAO;YAEjG,gCAAgC;YAChC,MAAM,YAAY,IAAI,IAAI,gBAAgB,GAAG,CAAC,CAAC,KAAO;oBAAC,GAAG,UAAU;oBAAE,GAAG,MAAM;iBAAC;YAEhF,OAAQ;gBACN,KAAK,mIAAA,CAAA,iBAAc,CAAC,YAAY;oBAC9B,iBAAiB;oBACjB,MAAM,oBAAoB,CAAC,SAAS,GAAG,MAAM,oBAAoB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;wBAC/E,MAAM,SAAS,UAAU,GAAG,CAAC,SAAS,EAAE;wBACxC,IAAI,WAAW,WAAW;4BACxB,OAAO;gCAAE,GAAG,QAAQ;gCAAE;4BAAO;wBAC/B;wBACA,OAAO;oBACT;oBAEA,eAAe;oBACf,MAAM,oBAAoB,CAAC,KAAK,GAAG;oBACnC;gBAEF,KAAK,mIAAA,CAAA,iBAAc,CAAC,aAAa;oBAC/B,IAAI,UAAU;wBACZ,0CAA0C;wBAC1C,IAAI,CAAC,MAAM,qBAAqB,CAAC,SAAS,EAAE;4BAC1C,MAAM,qBAAqB,CAAC,SAAS,GAAG;gCACtC,WAAW,EAAE;gCACb,OAAO;4BACT;wBACF;wBAEA,iBAAiB;wBACjB,MAAM,qBAAqB,CAAC,SAAS,CAAC,SAAS,GAAG,MAAM,qBAAqB,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;4BACrG,MAAM,SAAS,UAAU,GAAG,CAAC,SAAS,EAAE;4BACxC,IAAI,WAAW,WAAW;gCACxB,OAAO;oCAAE,GAAG,QAAQ;oCAAE;gCAAO;4BAC/B;4BACA,OAAO;wBACT;wBAEA,oCAAoC;wBACpC,MAAM,qBAAqB,CAAC,SAAS,CAAC,KAAK,GAAG;wBAC9C,MAAM,qBAAqB,CAAC,SAAS,CAAC,eAAe,GAAG;oBAC1D;oBACA;gBAEF,KAAK,mIAAA,CAAA,iBAAc,CAAC,gBAAgB;oBAClC,IAAI,UAAU;wBACZ,0CAA0C;wBAC1C,IAAI,CAAC,MAAM,wBAAwB,CAAC,SAAS,EAAE;4BAC7C,MAAM,wBAAwB,CAAC,SAAS,GAAG;gCACzC,WAAW,EAAE;gCACb,OAAO;4BACT;wBACF;wBAEA,iBAAiB;wBACjB,MAAM,wBAAwB,CAAC,SAAS,CAAC,SAAS,GAAG,MAAM,wBAAwB,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;4BAC3G,MAAM,SAAS,UAAU,GAAG,CAAC,SAAS,EAAE;4BACxC,IAAI,WAAW,WAAW;gCACxB,OAAO;oCAAE,GAAG,QAAQ;oCAAE;gCAAO;4BAC/B;4BACA,OAAO;wBACT;wBAEA,oCAAoC;wBACpC,MAAM,wBAAwB,CAAC,SAAS,CAAC,KAAK,GAAG;wBACjD,MAAM,wBAAwB,CAAC,SAAS,CAAC,eAAe,GAAG;oBAC7D;oBACA;YACJ;QACF;QAEA,gBAAgB,CAAC;YACf,gCAAgC;YAChC,MAAM,qBAAqB,GAAG,aAAa,qBAAqB;YAChE,MAAM,wBAAwB,GAAG,aAAa,wBAAwB;YACtE,MAAM,oBAAoB,GAAG,aAAa,oBAAoB;QAChE;IACF;AACF;AAEO,MAAM,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,cAAc,EAAE,6BAA6B,EAAE,GAAG,eAAe,OAAO;uCAErH,eAAe,OAAO", "debugId": null}}, {"offset": {"line": 663, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 669, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/redux/store.ts"], "sourcesContent": ["import { configureStore } from \"@reduxjs/toolkit\";\nimport { persistStore, persistReducer, FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER } from \"redux-persist\";\nimport storage from \"redux-persist/lib/storage\"; // defaults to localStorage for web\n\n// Import the reducers directly to avoid circular dependency\nimport jobSkillsReducer from \"./slices/jobSkillsSlice\";\nimport jobDetailsReducer from \"./slices/jobDetailsSlice\";\nimport allSkillsReducer from \"./slices/allSkillsSlice\";\nimport authReducer from \"./slices/authSlice\";\nimport jobRequirementReducer from \"./slices/jobRequirementSlice\";\nimport interviewReducer from \"./slices/interviewSlice\";\n\n// Configure persist options for job skills slice\nconst jobSkillsPersistConfig = {\n  key: \"jobSkills\",\n  storage,\n};\n\n// Configure persist options for job details slice\nconst jobDetailsPersistConfig = {\n  key: \"jobDetails\",\n  storage,\n};\n\n// Configure persist options for all skills slice\nconst allSkillsPersistConfig = {\n  key: \"allSkills\",\n  storage,\n  blacklist: [\"loading\", \"error\"], // Don't persist loading and error states\n};\n\n// Configure persist options for auth slice\nconst authPersistConfig = {\n  key: \"auth\",\n  storage,\n};\n// Configure persist options for job requirement slice\nconst jobRequirementPersistConfig = {\n  key: \"jobRequirement\",\n  storage,\n};\n\n// Configure persist options for interview questions slice\nconst interviewPersistConfig = {\n  key: \"interview\",\n  storage,\n};\n\n// Create persisted reducers\nconst persistedJobSkillsReducer = persistReducer(jobSkillsPersistConfig, jobSkillsReducer);\nconst persistedJobDetailsReducer = persistReducer(jobDetailsPersistConfig, jobDetailsReducer);\nconst persistedAllSkillsReducer = persistReducer(allSkillsPersistConfig, allSkillsReducer);\nconst persistedAuthReducer = persistReducer(authPersistConfig, authReducer);\nconst persistedJobRequirementReducer = persistReducer(jobRequirementPersistConfig, jobRequirementReducer);\nconst persistedInterviewReducer = persistReducer(interviewPersistConfig, interviewReducer);\n\n// Create store with the persisted reducers\nexport const store = configureStore({\n  reducer: {\n    jobSkills: persistedJobSkillsReducer,\n    jobDetails: persistedJobDetailsReducer,\n    allSkills: persistedAllSkillsReducer,\n    auth: persistedAuthReducer,\n    jobRequirement: persistedJobRequirementReducer,\n    interview: persistedInterviewReducer,\n  },\n  middleware: (getDefaultMiddleware) =>\n    getDefaultMiddleware({\n      serializableCheck: {\n        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],\n      },\n    }),\n});\n\n// Create persistor\nexport const persistor = persistStore(store);\n\n// Infer the `RootState` and `AppDispatch` types from the store itself\nexport type RootState = ReturnType<typeof store.getState>;\nexport type AppDispatch = typeof store.dispatch;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AACA,+QAAiD,mCAAmC;AAEpF,4DAA4D;AAC5D;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEA,iDAAiD;AACjD,MAAM,yBAAyB;IAC7B,KAAK;IACL,SAAA,2JAAA,CAAA,UAAO;AACT;AAEA,kDAAkD;AAClD,MAAM,0BAA0B;IAC9B,KAAK;IACL,SAAA,2JAAA,CAAA,UAAO;AACT;AAEA,iDAAiD;AACjD,MAAM,yBAAyB;IAC7B,KAAK;IACL,SAAA,2JAAA,CAAA,UAAO;IACP,WAAW;QAAC;QAAW;KAAQ;AACjC;AAEA,2CAA2C;AAC3C,MAAM,oBAAoB;IACxB,KAAK;IACL,SAAA,2JAAA,CAAA,UAAO;AACT;AACA,sDAAsD;AACtD,MAAM,8BAA8B;IAClC,KAAK;IACL,SAAA,2JAAA,CAAA,UAAO;AACT;AAEA,0DAA0D;AAC1D,MAAM,yBAAyB;IAC7B,KAAK;IACL,SAAA,2JAAA,CAAA,UAAO;AACT;AAEA,4BAA4B;AAC5B,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,wIAAA,CAAA,UAAgB;AACzF,MAAM,6BAA6B,CAAA,GAAA,qMAAA,CAAA,iBAAc,AAAD,EAAE,yBAAyB,yIAAA,CAAA,UAAiB;AAC5F,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,wIAAA,CAAA,UAAgB;AACzF,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,iBAAc,AAAD,EAAE,mBAAmB,mIAAA,CAAA,UAAW;AAC1E,MAAM,iCAAiC,CAAA,GAAA,qMAAA,CAAA,iBAAc,AAAD,EAAE,6BAA6B,6IAAA,CAAA,UAAqB;AACxG,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,wIAAA,CAAA,UAAgB;AAGlF,MAAM,QAAQ,CAAA,GAAA,2LAAA,CAAA,iBAAc,AAAD,EAAE;IAClC,SAAS;QACP,WAAW;QACX,YAAY;QACZ,WAAW;QACX,MAAM;QACN,gBAAgB;QAChB,WAAW;IACb;IACA,YAAY,CAAC,uBACX,qBAAqB;YACnB,mBAAmB;gBACjB,gBAAgB;oBAAC,mJAAA,CAAA,QAAK;oBAAE,mJAAA,CAAA,YAAS;oBAAE,mJAAA,CAAA,QAAK;oBAAE,mJAAA,CAAA,UAAO;oBAAE,mJAAA,CAAA,QAAK;oBAAE,mJAAA,CAAA,WAAQ;iBAAC;YACrE;QACF;AACJ;AAGO,MAAM,YAAY,CAAA,GAAA,iMAAA,CAAA,eAAY,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 759, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 765, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/redux/ReduxProvider.tsx"], "sourcesContent": ["\"use client\";\nimport React from \"react\";\nimport { Provider } from \"react-redux\";\nimport { PersistGate } from \"redux-persist/integration/react\";\nimport { store, persistor } from \"./store\";\n\ninterface ReduxProviderProps {\n  children: React.ReactNode;\n}\n\nconst ReduxProvider: React.FC<ReduxProviderProps> = ({ children }) => {\n  return (\n    <Provider store={store}>\n      <PersistGate persistor={persistor}>{children}</PersistGate>\n    </Provider>\n  );\n};\n\nexport default ReduxProvider;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAUA,MAAM,gBAA8C,CAAC,EAAE,QAAQ,EAAE;IAC/D,qBACE,8OAAC,yJAAA,CAAA,WAAQ;QAAC,OAAO,qHAAA,CAAA,QAAK;kBACpB,cAAA,8OAAC,8JAAA,CAAA,cAAW;YAAC,WAAW,qHAAA,CAAA,YAAS;sBAAG;;;;;;;;;;;AAG1C;uCAEe", "debugId": null}}, {"offset": {"line": 795, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 825, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/utils/syncReduxToCookies.ts"], "sourcesContent": ["import { PERMISSIONS_COOKIES_KEY } from \"@/constants/commonConstants\";\nimport { Permission } from \"@/interfaces/authInterfaces\";\nimport { store } from \"@/redux/store\";\nimport Cookies from \"js-cookie\";\n\n// Serialize specific parts of Redux state to cookies\nexport const syncReduxStateToCookies = (permissions?: Permission[], forceSync = false) => {\n  try {\n    const permissionData = Cookies.get(PERMISSIONS_COOKIES_KEY);\n    if (!forceSync && permissionData) {\n      return;\n    }\n    const state = store.getState();\n\n    // Sync auth state to cookies (permissions are in auth state)\n    if (state.auth) {\n      Cookies.set(PERMISSIONS_COOKIES_KEY, JSON.stringify(permissions?.length ? permissions : state.auth.permissions), {\n        expires: 4, // 4 day\n        path: \"/\",\n        sameSite: \"strict\",\n      });\n    }\n    console.log(\"Redux state synced to cookies\");\n  } catch (error) {\n    console.error(\"Error syncing Redux state to cookies:\", error);\n  }\n};\n"], "names": [], "mappings": ";;;AAAA;AAEA;AACA;;;;AAGO,MAAM,0BAA0B,CAAC,aAA4B,YAAY,KAAK;IACnF,IAAI;QACF,MAAM,iBAAiB,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mIAAA,CAAA,0BAAuB;QAC1D,IAAI,CAAC,aAAa,gBAAgB;YAChC;QACF;QACA,MAAM,QAAQ,qHAAA,CAAA,QAAK,CAAC,QAAQ;QAE5B,6DAA6D;QAC7D,IAAI,MAAM,IAAI,EAAE;YACd,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mIAAA,CAAA,0BAAuB,EAAE,KAAK,SAAS,CAAC,aAAa,SAAS,cAAc,MAAM,IAAI,CAAC,WAAW,GAAG;gBAC/G,SAAS;gBACT,MAAM;gBACN,UAAU;YACZ;QACF;QACA,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;IACzD;AACF", "debugId": null}}, {"offset": {"line": 854, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 865, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/logo.svg.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 257, height: 70, blurDataURL: null, blurWidth: 0, blurHeight: 0 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,2HAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAI,aAAa;IAAM,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 878, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 889, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/down-arrow.svg.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 15, height: 8, blurDataURL: null, blurWidth: 0, blurHeight: 0 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,oIAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAG,aAAa;IAAM,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 902, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 913, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/user.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 51, height: 50, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAABE0lEQVR42gEIAff+AAICAgEzMzM5iIiHuaqckfWjl472i4qKvDU1NT4CAgICAC8vLzSXl5fQq6ai/tCpjv/HpIr/q6el/piYmNYyMjI5AH9/f6qsrKz+rqqn/82hiP/UqIz/sKuo/6ysrP6FhYWxAKWlpeaoqav/k5ah/6SIfP+wkH//kZSf/6eoq/+pqansAKSkpeWCip3/UGGI/25sfP93cn//U2KJ/4CInf+np6fsAHt8fqhlcpH+SlqC/0xbgv9HWID/SluF/2hzkf5/gIKyACUnKzNLWXrQc3KC/qWWj/+Cf4z/UmCG/lZhfdYsLS86AAEBAgEWGyc6P0ZduZ2BcPWQfHH2PUdhvRkdJz4BAQICbTiKQnEKIQsAAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,2HAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsd,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 926, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 931, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/header.module.scss.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"header-module-scss-module__4lyeFq__active\",\n  \"admin_info\": \"header-module-scss-module__4lyeFq__admin_info\",\n  \"align_header_search\": \"header-module-scss-module__4lyeFq__align_header_search\",\n  \"circle_img\": \"header-module-scss-module__4lyeFq__circle_img\",\n  \"dropdown_menu\": \"header-module-scss-module__4lyeFq__dropdown_menu\",\n  \"header\": \"header-module-scss-module__4lyeFq__header\",\n  \"header_buttons\": \"header-module-scss-module__4lyeFq__header_buttons\",\n  \"header_right\": \"header-module-scss-module__4lyeFq__header_right\",\n  \"hidden\": \"header-module-scss-module__4lyeFq__hidden\",\n  \"logo\": \"header-module-scss-module__4lyeFq__logo\",\n  \"navbar_content\": \"header-module-scss-module__4lyeFq__navbar_content\",\n  \"navbar_links\": \"header-module-scss-module__4lyeFq__navbar_links\",\n  \"open\": \"header-module-scss-module__4lyeFq__open\",\n  \"searchBar\": \"header-module-scss-module__4lyeFq__searchBar\",\n  \"searchButton\": \"header-module-scss-module__4lyeFq__searchButton\",\n  \"searchContainer\": \"header-module-scss-module__4lyeFq__searchContainer\",\n  \"searchIcon\": \"header-module-scss-module__4lyeFq__searchIcon\",\n  \"searchInput\": \"header-module-scss-module__4lyeFq__searchInput\",\n  \"search_wrapper\": \"header-module-scss-module__4lyeFq__search_wrapper\",\n  \"show\": \"header-module-scss-module__4lyeFq__show\",\n  \"user_drop\": \"header-module-scss-module__4lyeFq__user_drop\",\n  \"user_drop_btn\": \"header-module-scss-module__4lyeFq__user_drop_btn\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 955, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 961, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/Notification.tsx"], "sourcesContent": ["import React from \"react\";\n\ninterface NotificationIconProps extends React.SVGProps<SVGSVGElement> {\n  hasNotification?: boolean;\n  onClick?: React.MouseEventHandler<SVGSVGElement>; // Added this line\n}\n\nfunction NotificationIcon(props: NotificationIconProps) {\n  const { hasNotification, ...restProps } = props;\n  return (\n    <svg {...restProps} className=\"cursor-pointer\" xmlns=\"http://www.w3.org/2000/svg\" width=\"25\" height=\"24\" viewBox=\"0 0 33 32\" fill=\"none\">\n      <path\n        d=\"M27.458 22.9624C26.251 20.8511 25.6133 18.4492 25.6133 16.0166C25.6133 16.0166 25.6133 14.0014 25.6133 14C25.6133 10.1198 22.9443 6.54149 19.2454 5.40924C19.4725 4.98712 19.6133 4.51202 19.6133 4C19.6133 2.3457 18.2676 1 16.6133 1C14.959 1 13.6133 2.3457 13.6133 4C13.6133 4.51233 13.7544 4.98767 13.9817 5.40997C10.2878 6.57581 7.61332 10.1457 7.61332 14.3071V16.0166C7.61332 18.4492 6.97562 20.8511 5.76811 22.9629C5.1221 24.0927 4.75006 25.2737 5.46489 26.5054C6.00736 27.4414 6.97758 28 8.05961 28H12.6133C12.6133 30.2056 14.4077 32 16.6133 32C18.8189 32 20.6133 30.2056 20.6133 28H25.167C26.249 28 27.2193 27.4414 27.7617 26.5054C28.4522 25.3141 28.0953 24.0784 27.458 22.9624ZM16.6133 3C17.1646 3 17.6133 3.44873 17.6133 4C17.6133 4.55127 17.1646 5 16.6133 5C16.062 5 15.6133 4.55127 15.6133 4C15.6133 3.44873 16.062 3 16.6133 3ZM16.6133 30C15.5103 30 14.6133 29.103 14.6133 28H18.6133C18.6133 29.103 17.7163 30 16.6133 30ZM26.0323 25.5019C25.9453 25.6514 25.687 26 25.167 26H8.05961C7.53967 26 7.28136 25.6515 7.19441 25.502C6.87823 24.9586 7.23496 24.428 7.50492 23.9546C8.88432 21.542 9.61332 18.7969 9.61332 16.0166C9.61332 16.0166 9.61332 14.3081 9.61332 14.3071C9.61332 10.5303 12.7077 7.00054 16.602 7.00049C20.3752 7.00044 23.6133 10.2392 23.6133 14V16.0166C23.6133 18.7968 24.3423 21.5419 25.7212 23.954C26.0017 24.4448 26.3567 24.9391 26.0323 25.5019Z\"\n        fill=\"#333333\"\n      />\n      {hasNotification && <circle cx=\"24.6136\" cy=\"10.6654\" r=\"4.83333\" fill=\"#D4000D\" stroke=\"white\" />}\n    </svg>\n  );\n}\n\nexport default NotificationIcon;\n"], "names": [], "mappings": ";;;;;AAOA,SAAS,iBAAiB,KAA4B;IACpD,MAAM,EAAE,eAAe,EAAE,GAAG,WAAW,GAAG;IAC1C,qBACE,8OAAC;QAAK,GAAG,SAAS;QAAE,WAAU;QAAiB,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;;0BAChI,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;YAEN,iCAAmB,8OAAC;gBAAO,IAAG;gBAAU,IAAG;gBAAU,GAAE;gBAAU,MAAK;gBAAU,QAAO;;;;;;;;;;;;AAG9F;uCAEe", "debugId": null}}, {"offset": {"line": 1004, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1010, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/utils/storage.ts"], "sourcesContent": ["import SecureLS from \"secure-ls\";\n\nconst ls = new SecureLS();\n\ninterface Storage {\n  set: (key: string, data: unknown) => void;\n  get: (key: string) => unknown;\n  remove: (key: string) => void;\n  removeAll: () => void;\n  getAllKeys: () => string[];\n}\n\nconst storage: Storage = {\n  set: (key, data) => {\n    if (typeof window !== \"undefined\") {\n      ls.set(key, JSON.stringify(data));\n    }\n  },\n\n  get: (key) => {\n    if (typeof window !== \"undefined\") {\n      const data = ls.get(key);\n      if (data) {\n        return data ? JSON.parse(data) : null;\n      }\n    }\n    return null;\n  },\n\n  remove: (key) => {\n    if (typeof window !== \"undefined\") {\n      ls.remove(key);\n    }\n  },\n\n  removeAll: () => {\n    ls.removeAll();\n    if (typeof window !== \"undefined\") {\n      localStorage.clear();\n    }\n  },\n\n  getAllKeys: () => {\n    if (typeof window !== \"undefined\") {\n      const keys: string[] = ls.getAllKeys();\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key) {\n          keys.push(key);\n        }\n      }\n      return keys;\n    }\n    return [];\n  },\n};\n\nexport default storage;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,KAAK,IAAI,oJAAA,CAAA,UAAQ;AAUvB,MAAM,UAAmB;IACvB,KAAK,CAAC,KAAK;QACT,uCAAmC;;QAEnC;IACF;IAEA,KAAK,CAAC;QACJ,uCAAmC;;QAKnC;QACA,OAAO;IACT;IAEA,QAAQ,CAAC;QACP,uCAAmC;;QAEnC;IACF;IAEA,WAAW;QACT,GAAG,SAAS;QACZ,uCAAmC;;QAEnC;IACF;IAEA,YAAY;QACV,uCAAmC;;QASnC;QACA,OAAO,EAAE;IACX;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 1047, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1053, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/constants/endpoint.ts"], "sourcesContent": ["// import config from \"@/config/config\";\n\nconst URL = process.env.NEXT_PUBLIC_BASE_URL;\n\nconst endpoint = {\n  auth: {\n    SIGNIN: `${URL}/auth/sign-in`,\n    VERIFY_OTP: `${URL}/auth/verify-otp`,\n    RESEND_OTP: `${URL}/auth/resend-otp`,\n    FORGOT_PASSWORD: `${URL}/auth/forgot-password`,\n    RESET_PASSWORD: `${URL}/auth/reset-password`,\n    DELETE_SESSION: `${URL}/auth/delete-session`,\n    UPDATE_TIMEZONE: `${URL}/auth/update-timezone`,\n  },\n  interview: {\n    UPDATE_OR_SCHEDULE_INTERVIEW: `${URL}/interview/update-or-schedule-interview`,\n    GET_INTERVIEWS: `${URL}/interview/get-interviews`,\n    GET_INTERVIEWERS: `${URL}/interview/get-interviewers`,\n    GET_MY_INTERVIEWS: `${URL}/interview/get-my-interviews`,\n    UPDATE_INTERVIEW_ANSWERS: `${URL}/interview/update-interview-answers`,\n\n    GET_UPCOMING_OR_PAST_INTERVIEW: `${URL}/interview/get-upcoming-or-past-interviews`,\n    GET_INTERVIEW_SKILL_QUESTIONS: `${URL}/interview/get-interview-skill-questions`,\n    UPDATE_INTERVIEW_SKILL_QUESTION: `${URL}/interview/update-interview-skill-question`,\n    ADD_INTERVIEW_SKILL_QUESTION: `${URL}/interview/add-interview-skill-question`,\n    GET_COMPLETED_SKILLS: `${URL}/interview/get-completed-skills`,\n\n    GET_JOB_LIST: `${URL}/interview/get-job-list`,\n    GET_CANDIDATE_LIST: `${URL}/interview/get-candidate-list`,\n    END_INTERVIEW: `${URL}/interview/end-interview`,\n    CONDUCT_INTERVIEW_STATIC_INFORMATION: `${URL}/interview/conduct-interview-static-information`,\n  },\n  common: {\n    REMOVE_ATTACHMENTS_FROM_S3: `${URL}/remove-attachments-from-s3`,\n    GENERATE_PRESIGNED_URL: `${URL}/generate-presignedurl`,\n  },\n  jobRequirements: {\n    GENERATE_SKILL: `${URL}/jobs/generate-skills`,\n    UPLOAD_URL: `${URL}/jobs/upload-url`,\n    PARSE_PDF: `${URL}/jobs/parse-pdf`,\n    GET_ALL_SKILLS: `${URL}/jobs/get-all-skills`,\n    GENERATE_JOB_REQUIREMENT: `${URL}/jobs/generate-job-requirement`,\n    SAVE_JOB_DETAILS: `${URL}/jobs/save-job-details`,\n    GET_JOBS_META: `${URL}/jobs/get-jobs-meta`, // <-- Full URL here\n    UPDATE_JOB_STATUS: \"/jobs/updateJob\",\n    GET_JOB_HTML_DESCRIPTION: `${URL}/jobs/get-job-html-description`,\n    UPDATE_JOB_DESCRIPTION: `${URL}/jobs/update-job-description`,\n    GENERATE_PDF: `${URL}/jobs/generate-pdf`,\n  },\n  Dashboard: {\n    GET_DASHBOARD_COUNTS: `${URL}/jobs/dashboard-counts`,\n  },\n  resumeScreen: {\n    MANUAL_CANDIDATE_UPLOAD: `${URL}/resume-screen/manual-candidate-upload`,\n    GET_PRESIGNED_URL: `${URL}/resume-screen/get-presigned-url`,\n    GET_ALL_PENDING_JOB_APPLICATIONS: `${URL}/resume-screen/get-all-pending-job-applications`,\n    CHANGE_APPLICATION_STATUS: `${URL}/resume-screen/change-application-status`,\n  },\n  employee: {\n    ADD_EMPLOYEES: `${URL}/employee-management/add-hiring-employee`,\n    GET_EMPLOYEES: `${URL}/employee-management`,\n    GET_EMPLOYEES_BY_DEPARTMENT: `${URL}/employee-management/employees`,\n    UPDATE_EMPLOYEE_ROLE: `${URL}/employee-management/employee/:employeeId/role`,\n\n    UPDATE_EMPLOYEE_INTERVIEW_ORDER: `${URL}/employee-management/employee/:employeeId/interview-order`,\n  },\n  userprofile: {\n    GET_MY_PROFILE: `${URL}/user-profile/get-my-profile`,\n    UPDATE_MY_PROFILE: `${URL}/user-profile/update-my-profile`,\n  },\n\n  roles: {\n    GET_ROLES: `${URL}/access-management/user-roles`,\n    ADD_USER_ROLE: `${URL}/access-management/add-user-role`,\n    UPDATE_USER_ROLE: `${URL}/access-management/user-role`,\n    DELETE_USER_ROLE: `${URL}/access-management/user-role`,\n    GET_ROLE_PERMISSIONS: `${URL}/access-management/role-permissions`,\n    GET_ROLE_PERMISSIONS_BY_ID: `${URL}/access-management/role-permissions/:roleId`,\n    UPDATE_ROLE_PERMISSIONS: `${URL}/access-management/role-permissions/:roleId`,\n    USER_PERMISSIONS: `${URL}/access-management/user-permissions`,\n  },\n\n  departments: {\n    GET_DEPARTMENTS: `${URL}/employee-management/departments`,\n    ADD_DEPARTMENT: `${URL}/employee-management/add-department`,\n    UPDATE_DEPARTMENT: `${URL}/employee-management/update-department/:departmentId`,\n    DELETE_DEPARTMENT: `${URL}/employee-management/delete-department/:departmentId`,\n  },\n\n  assessment: {\n    CREATE_FINAL_ASSESSMENT: `${URL}/final-assessment/create-final-assessment`,\n    GET_FINAL_ASSESSMENT_QUESTIONS: `${URL}/final-assessment/assessment/questions`,\n    CREATE_ASSESSMENT_QUESTION: `${URL}/final-assessment/assessment/create-question`,\n    SUBMIT_CANDIDATE_ANSWERS: `${URL}/final-assessment/candidate/:candidateId/submit`,\n    SHARE_ASSESSMENT: `${URL}/final-assessment/assessment/share`,\n    GET_FINAL_ASSESSMENT_BY_CANDIDATE: `${URL}/final-assessment/candidate/assessment`,\n    SUBMIT_ASSESSMENT: `${URL}/final-assessment/candidate/assessment/submit`,\n    GET_ASSESSMENT_STATUS: `${URL}/final-assessment/assessment-status`,\n    VERIFY_CANDIDATE_EMAIL: `${URL}/final-assessment/candidate/verify-email`,\n    GENERATE_ASSESSMENT_TOKEN: `${URL}/final-assessment/assessment/generate-token`,\n  },\n  candidatesApplication: {\n    ADDITIONAL_INFO: `${URL}/candidates/add-applicant-additional-info`,\n    PROMOTE_DEMOTE_CANDIDATE: `${URL}/candidates/update-candidate-rank-status`, // Base URL for candidates-related endpoints\n    GET_TOP_CANDIDATES_WITH_APPLICATIONS: `${URL}/candidates/top-candidates`,\n    GET_CANDIDATES_WITH_APPLICATIONS: `${URL}/candidates/get-candidates`,\n    ARCHIVE_ACTIVE_APPLICATION: `${URL}/candidates/archive-active-application/:applicationId`,\n    GET_CANDIDATE_DETAILS: `${URL}/candidates/get-candidate-details`, // New endpoint for individual candidate details\n    UPDATE_JOB_APPLICATION_STATUS: `${URL}/candidates/update-job-application-status/:jobApplicationId`, // Endpoint for updating job application status\n  },\n};\n\nexport default endpoint;\n"], "names": [], "mappings": "AAAA,wCAAwC;;;;AAExC,MAAM;AAEN,MAAM,WAAW;IACf,MAAM;QACJ,QAAQ,GAAG,IAAI,aAAa,CAAC;QAC7B,YAAY,GAAG,IAAI,gBAAgB,CAAC;QACpC,YAAY,GAAG,IAAI,gBAAgB,CAAC;QACpC,iBAAiB,GAAG,IAAI,qBAAqB,CAAC;QAC9C,gBAAgB,GAAG,IAAI,oBAAoB,CAAC;QAC5C,gBAAgB,GAAG,IAAI,oBAAoB,CAAC;QAC5C,iBAAiB,GAAG,IAAI,qBAAqB,CAAC;IAChD;IACA,WAAW;QACT,8BAA8B,GAAG,IAAI,uCAAuC,CAAC;QAC7E,gBAAgB,GAAG,IAAI,yBAAyB,CAAC;QACjD,kBAAkB,GAAG,IAAI,2BAA2B,CAAC;QACrD,mBAAmB,GAAG,IAAI,4BAA4B,CAAC;QACvD,0BAA0B,GAAG,IAAI,mCAAmC,CAAC;QAErE,gCAAgC,GAAG,IAAI,0CAA0C,CAAC;QAClF,+BAA+B,GAAG,IAAI,wCAAwC,CAAC;QAC/E,iCAAiC,GAAG,IAAI,0CAA0C,CAAC;QACnF,8BAA8B,GAAG,IAAI,uCAAuC,CAAC;QAC7E,sBAAsB,GAAG,IAAI,+BAA+B,CAAC;QAE7D,cAAc,GAAG,IAAI,uBAAuB,CAAC;QAC7C,oBAAoB,GAAG,IAAI,6BAA6B,CAAC;QACzD,eAAe,GAAG,IAAI,wBAAwB,CAAC;QAC/C,sCAAsC,GAAG,IAAI,+CAA+C,CAAC;IAC/F;IACA,QAAQ;QACN,4BAA4B,GAAG,IAAI,2BAA2B,CAAC;QAC/D,wBAAwB,GAAG,IAAI,sBAAsB,CAAC;IACxD;IACA,iBAAiB;QACf,gBAAgB,GAAG,IAAI,qBAAqB,CAAC;QAC7C,YAAY,GAAG,IAAI,gBAAgB,CAAC;QACpC,WAAW,GAAG,IAAI,eAAe,CAAC;QAClC,gBAAgB,GAAG,IAAI,oBAAoB,CAAC;QAC5C,0BAA0B,GAAG,IAAI,8BAA8B,CAAC;QAChE,kBAAkB,GAAG,IAAI,sBAAsB,CAAC;QAChD,eAAe,GAAG,IAAI,mBAAmB,CAAC;QAC1C,mBAAmB;QACnB,0BAA0B,GAAG,IAAI,8BAA8B,CAAC;QAChE,wBAAwB,GAAG,IAAI,4BAA4B,CAAC;QAC5D,cAAc,GAAG,IAAI,kBAAkB,CAAC;IAC1C;IACA,WAAW;QACT,sBAAsB,GAAG,IAAI,sBAAsB,CAAC;IACtD;IACA,cAAc;QACZ,yBAAyB,GAAG,IAAI,sCAAsC,CAAC;QACvE,mBAAmB,GAAG,IAAI,gCAAgC,CAAC;QAC3D,kCAAkC,GAAG,IAAI,+CAA+C,CAAC;QACzF,2BAA2B,GAAG,IAAI,wCAAwC,CAAC;IAC7E;IACA,UAAU;QACR,eAAe,GAAG,IAAI,wCAAwC,CAAC;QAC/D,eAAe,GAAG,IAAI,oBAAoB,CAAC;QAC3C,6BAA6B,GAAG,IAAI,8BAA8B,CAAC;QACnE,sBAAsB,GAAG,IAAI,8CAA8C,CAAC;QAE5E,iCAAiC,GAAG,IAAI,yDAAyD,CAAC;IACpG;IACA,aAAa;QACX,gBAAgB,GAAG,IAAI,4BAA4B,CAAC;QACpD,mBAAmB,GAAG,IAAI,+BAA+B,CAAC;IAC5D;IAEA,OAAO;QACL,WAAW,GAAG,IAAI,6BAA6B,CAAC;QAChD,eAAe,GAAG,IAAI,gCAAgC,CAAC;QACvD,kBAAkB,GAAG,IAAI,4BAA4B,CAAC;QACtD,kBAAkB,GAAG,IAAI,4BAA4B,CAAC;QACtD,sBAAsB,GAAG,IAAI,mCAAmC,CAAC;QACjE,4BAA4B,GAAG,IAAI,2CAA2C,CAAC;QAC/E,yBAAyB,GAAG,IAAI,2CAA2C,CAAC;QAC5E,kBAAkB,GAAG,IAAI,mCAAmC,CAAC;IAC/D;IAEA,aAAa;QACX,iBAAiB,GAAG,IAAI,gCAAgC,CAAC;QACzD,gBAAgB,GAAG,IAAI,mCAAmC,CAAC;QAC3D,mBAAmB,GAAG,IAAI,oDAAoD,CAAC;QAC/E,mBAAmB,GAAG,IAAI,oDAAoD,CAAC;IACjF;IAEA,YAAY;QACV,yBAAyB,GAAG,IAAI,yCAAyC,CAAC;QAC1E,gCAAgC,GAAG,IAAI,sCAAsC,CAAC;QAC9E,4BAA4B,GAAG,IAAI,4CAA4C,CAAC;QAChF,0BAA0B,GAAG,IAAI,+CAA+C,CAAC;QACjF,kBAAkB,GAAG,IAAI,kCAAkC,CAAC;QAC5D,mCAAmC,GAAG,IAAI,sCAAsC,CAAC;QACjF,mBAAmB,GAAG,IAAI,6CAA6C,CAAC;QACxE,uBAAuB,GAAG,IAAI,mCAAmC,CAAC;QAClE,wBAAwB,GAAG,IAAI,wCAAwC,CAAC;QACxE,2BAA2B,GAAG,IAAI,2CAA2C,CAAC;IAChF;IACA,uBAAuB;QACrB,iBAAiB,GAAG,IAAI,yCAAyC,CAAC;QAClE,0BAA0B,GAAG,IAAI,wCAAwC,CAAC;QAC1E,sCAAsC,GAAG,IAAI,0BAA0B,CAAC;QACxE,kCAAkC,GAAG,IAAI,0BAA0B,CAAC;QACpE,4BAA4B,GAAG,IAAI,qDAAqD,CAAC;QACzF,uBAAuB,GAAG,IAAI,iCAAiC,CAAC;QAChE,+BAA+B,GAAG,IAAI,2DAA2D,CAAC;IACpG;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 1160, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1270, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/config/config.ts"], "sourcesContent": ["const config = {\n  env: process.env.NODE_ENV,\n  apiBaseUrl: process.env.NEXT_PUBLIC_BASE_URL,\n};\n\nexport default config;\n"], "names": [], "mappings": ";;;AAAA,MAAM,SAAS;IACb,GAAG;IACH,UAAU;AACZ;uCAEe", "debugId": null}}, {"offset": {"line": 1278, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1284, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/utils/api.ts"], "sourcesContent": ["import { ApiResponse } from \"@/interfaces/commonInterfaces\";\nimport { TApiState } from \"@/types/types\";\n\nconst { toString } = Object.prototype;\n\nexport const isObject = <T>(arg: T): boolean => toString.call(arg) === \"[object Object]\";\n\nexport const withError = <T extends TApiState>(arg: T): ApiResponse => {\n  if (isObject(arg)) {\n    return {\n      data: null,\n      error: {\n        ...arg,\n      },\n    };\n  }\n\n  return {\n    data: null,\n    error: {\n      message: arg,\n    },\n  };\n};\n\nexport const withData = <T extends TApiState>(data: T): ApiResponse => ({\n  error: null,\n  data,\n});\n"], "names": [], "mappings": ";;;;;AAGA,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,SAAS;AAE9B,MAAM,WAAW,CAAI,MAAoB,SAAS,IAAI,CAAC,SAAS;AAEhE,MAAM,YAAY,CAAsB;IAC7C,IAAI,SAAS,MAAM;QACjB,OAAO;YACL,MAAM;YACN,OAAO;gBACL,GAAG,GAAG;YACR;QACF;IACF;IAEA,OAAO;QACL,MAAM;QACN,OAAO;YACL,SAAS;QACX;IACF;AACF;AAEO,MAAM,WAAW,CAAsB,OAAyB,CAAC;QACtE,OAAO;QACP;IACF,CAAC", "debugId": null}}, {"offset": {"line": 1311, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1317, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/utils/helper.ts"], "sourcesContent": ["import { signOut } from \"next-auth/react\";\nimport Cookies from \"js-cookie\";\nimport toast from \"react-hot-toast\";\n\nimport storage from \"./storage\";\n\nimport { ACCESS_TOKEN_KEY, PERMISSIONS_COOKIES_KEY } from \"@/constants/commonConstants\";\nimport { deleteSession } from \"@/services/authServices\";\nimport { getSignedUrl } from \"@/services/commonService\";\nimport { FilePath } from \"@/interfaces/commonInterfaces\";\n\nexport const getAccessToken = () => {\n  return storage.get(ACCESS_TOKEN_KEY);\n};\n\nexport const clearStorage = () => {\n  return storage.removeAll();\n};\n\nexport const setAccessToken = (accessToken: string) => {\n  storage.set(ACCESS_TOKEN_KEY, accessToken);\n};\n\n/**\n * Toast style object\n */\nconst style = {\n  fontSize: \"16px\",\n};\n\n/**\n * Toast success message\n * @param message - The message to display\n */\nexport const toastMessageSuccess = (message: string) => {\n  toast.success(message, {\n    style,\n  });\n};\n\n/**\n * Toast success message with icon\n * @param message - The message to display\n * @param icon - The icon to display\n */\nexport const toastMessageWithIcon = (message: string, icon: string) => {\n  toast.success(message, {\n    style,\n    icon,\n  });\n};\n\n/**\n * Toast error message\n * @param message - The message to display\n */\nexport const toastMessageError = (message: string) => {\n  toast.error(message, {\n    style,\n  });\n};\n\nexport const logout = async (userId?: number) => {\n  try {\n    deleteSession(userId);\n    await signOut({ redirect: false });\n    clearStorage();\n\n    // Delete permissions_data cookies when user logs out\n    Cookies.remove(PERMISSIONS_COOKIES_KEY, { path: \"/\" });\n  } catch (error) {\n    console.error(\"Error in logout:\", error);\n  }\n};\n\n/**\n *  get presignedUrl for image upload\n */\nexport const uploadFileOnS3 = async (file: Blob, filePath: string) => {\n  let body: FilePath = {\n    filePath: \"\",\n    fileFormat: \"\",\n  };\n  body = {\n    filePath,\n    fileFormat: file.type as string,\n  };\n  let signedUrl;\n  const presignedUrl = await getSignedUrl(body);\n  if (presignedUrl && presignedUrl.data) {\n    const response = await pushFileToS3(presignedUrl.data.data, file);\n    if (response?.url) {\n      signedUrl = response?.url.split(\"?\")?.[0];\n    }\n  }\n\n  return signedUrl?.replace(`${process.env.NEXT_PUBLIC_S3_URL}`, `${process.env.NEXT_PUBLIC_S3_CDN_URL}`);\n};\n\n/**\n *  Upload file on presignedUrl of S3\n */\nexport const pushFileToS3 = async (signedUrl: string, file: Blob): Promise<Response> => {\n  return fetch(signedUrl, {\n    method: \"PUT\",\n    body: file,\n    headers: {\n      \"Content-Type\": file.type,\n    },\n  });\n};\n\nexport const formatDate = (dateString: string) => {\n  const date = new Date(dateString);\n  return date.toLocaleDateString(\"en-US\", {\n    year: \"numeric\",\n    month: \"long\",\n    day: \"numeric\",\n  });\n};\n// Format times as HH:MM for time inputs\nexport const formatTimeForInput = (date: Date) => {\n  const hours = date.getHours().toString().padStart(2, \"0\");\n  const minutes = date.getMinutes().toString().padStart(2, \"0\");\n  return `${hours}:${minutes}`;\n};\n\nexport const toTitleCase = (name: string) => {\n  if (!name) return \"\";\n  return name\n    .toLowerCase()\n    .split(\" \")\n    .filter((word) => word) // remove extra spaces\n    .map((word) => word[0].toUpperCase() + word.slice(1))\n    .join(\" \");\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AACA;AACA;AAEA;AAEA;AACA;AACA;;;;;;;;AAGO,MAAM,iBAAiB;IAC5B,OAAO,uHAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mIAAA,CAAA,mBAAgB;AACrC;AAEO,MAAM,eAAe;IAC1B,OAAO,uHAAA,CAAA,UAAO,CAAC,SAAS;AAC1B;AAEO,MAAM,iBAAiB,CAAC;IAC7B,uHAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mIAAA,CAAA,mBAAgB,EAAE;AAChC;AAEA;;CAEC,GACD,MAAM,QAAQ;IACZ,UAAU;AACZ;AAMO,MAAM,sBAAsB,CAAC;IAClC,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC,SAAS;QACrB;IACF;AACF;AAOO,MAAM,uBAAuB,CAAC,SAAiB;IACpD,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC,SAAS;QACrB;QACA;IACF;AACF;AAMO,MAAM,oBAAoB,CAAC;IAChC,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,SAAS;QACnB;IACF;AACF;AAEO,MAAM,SAAS,OAAO;IAC3B,IAAI;QACF,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE;QACd,MAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE;YAAE,UAAU;QAAM;QAChC;QAEA,qDAAqD;QACrD,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC,mIAAA,CAAA,0BAAuB,EAAE;YAAE,MAAM;QAAI;IACtD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;IACpC;AACF;AAKO,MAAM,iBAAiB,OAAO,MAAY;IAC/C,IAAI,OAAiB;QACnB,UAAU;QACV,YAAY;IACd;IACA,OAAO;QACL;QACA,YAAY,KAAK,IAAI;IACvB;IACA,IAAI;IACJ,MAAM,eAAe,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD,EAAE;IACxC,IAAI,gBAAgB,aAAa,IAAI,EAAE;QACrC,MAAM,WAAW,MAAM,aAAa,aAAa,IAAI,CAAC,IAAI,EAAE;QAC5D,IAAI,UAAU,KAAK;YACjB,YAAY,UAAU,IAAI,MAAM,MAAM,CAAC,EAAE;QAC3C;IACF;IAEA,OAAO,WAAW,QAAQ,GAAG,QAAQ,GAAG,CAAC,kBAAkB,EAAE,EAAE,GAAG,QAAQ,GAAG,CAAC,sBAAsB,EAAE;AACxG;AAKO,MAAM,eAAe,OAAO,WAAmB;IACpD,OAAO,MAAM,WAAW;QACtB,QAAQ;QACR,MAAM;QACN,SAAS;YACP,gBAAgB,KAAK,IAAI;QAC3B;IACF;AACF;AAEO,MAAM,aAAa,CAAC;IACzB,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,MAAM,QAAQ,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;IACrD,MAAM,UAAU,KAAK,UAAU,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;IACzD,OAAO,GAAG,MAAM,CAAC,EAAE,SAAS;AAC9B;AAEO,MAAM,cAAc,CAAC;IAC1B,IAAI,CAAC,MAAM,OAAO;IAClB,OAAO,KACJ,WAAW,GACX,KAAK,CAAC,KACN,MAAM,CAAC,CAAC,OAAS,MAAM,sBAAsB;KAC7C,GAAG,CAAC,CAAC,OAAS,IAAI,CAAC,EAAE,CAAC,WAAW,KAAK,KAAK,KAAK,CAAC,IACjD,IAAI,CAAC;AACV", "debugId": null}}, {"offset": {"line": 1436, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1442, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/utils/http.ts"], "sourcesContent": ["// src/utils/http.ts\nimport axios, { AxiosResponse } from \"axios\";\nimport { getSession } from \"next-auth/react\";\n\nimport env from \"@/config/config\";\nimport { withData, withError } from \"@/utils/api\";\nimport { ISession } from \"@/interfaces/commonInterfaces\";\nimport { logout, toastMessageError } from \"./helper\";\nimport { TOKEN_EXPIRED } from \"@/constants/commonConstants\";\n\nexport const http = axios.create({\n  baseURL: env.apiBaseUrl,\n  headers: { \"Content-Type\": \"application/json\" },\n});\n\nhttp.interceptors.request.use(async (req) => {\n  const session = (await getSession()) as unknown as ISession;\n  const accessToken = session?.user?.data?.token;\n  if (accessToken) {\n    req.headers.authorization = `Bearer ${accessToken}`;\n  }\n  req.headers[\"ngrok-skip-browser-warning\"] = \"fjdlkghjsk\";\n  return req;\n});\n\n// Flag to prevent multiple logout calls\nlet isLoggingOut = false;\n\nhttp.interceptors.response.use(\n  (res) => withData(res.data) as AxiosResponse,\n  async (err) => {\n    const session = (await getSession()) as unknown as ISession;\n    const userId = session?.user?.data?.authData?.userData?.id;\n    const accessToken = session?.user?.data?.token;\n\n    if (err?.response?.status === 401 && !isLoggingOut && accessToken) {\n      isLoggingOut = true;\n      try {\n        await logout(userId);\n        toastMessageError(TOKEN_EXPIRED);\n\n        if (typeof window !== \"undefined\") {\n          window.location.reload();\n        }\n      } catch (error) {\n        console.error(\"Session cleanup error:\", error);\n      } finally {\n        isLoggingOut = false;\n      }\n    } else if (err?.response?.status === 403) {\n      // Show toast message for forbidden access (403)\n      toastMessageError(err?.response?.data?.message);\n    }\n    return withError(err?.response?.data?.error);\n  }\n);\n\nexport function get<P, R>(url: string, params?: P): Promise<R> {\n  return http({\n    method: \"get\",\n    url,\n    params,\n  });\n}\n\nexport function post<D, P, R>(url: string, data: D, params?: P): Promise<R> {\n  return http({\n    method: \"post\",\n    url,\n    data,\n    params,\n  });\n}\n\nexport function postFile<D, P, R>(url: string, data: D, params?: P): Promise<AxiosResponse<R>> {\n  return http({\n    method: \"post\",\n    url,\n    data,\n    params,\n    headers: { \"Content-Type\": \"multipart/form-data\" },\n  });\n}\nexport function put<D, P, R>(url: string, data: D, params?: P): Promise<R> {\n  return http({\n    method: \"put\",\n    url,\n    data,\n    params,\n  });\n}\n\nexport function patch<D, P, R>(url: string, data: D, params?: P): Promise<AxiosResponse<R>> {\n  return http({\n    method: \"patch\",\n    url,\n    data,\n    params,\n  });\n}\nexport function remove<P, R>(url: string, params?: P): Promise<R> {\n  return http({\n    method: \"delete\",\n    url,\n    params,\n  });\n}\n"], "names": [], "mappings": "AAAA,oBAAoB;;;;;;;;;;AACpB;AACA;AAEA;AACA;AAEA;AACA;;;;;;;AAEO,MAAM,OAAO,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC/B,SAAS,uHAAA,CAAA,UAAG,CAAC,UAAU;IACvB,SAAS;QAAE,gBAAgB;IAAmB;AAChD;AAEA,KAAK,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO;IACnC,MAAM,UAAW,MAAM,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAChC,MAAM,cAAc,SAAS,MAAM,MAAM;IACzC,IAAI,aAAa;QACf,IAAI,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,aAAa;IACrD;IACA,IAAI,OAAO,CAAC,6BAA6B,GAAG;IAC5C,OAAO;AACT;AAEA,wCAAwC;AACxC,IAAI,eAAe;AAEnB,KAAK,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC5B,CAAC,MAAQ,CAAA,GAAA,mHAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,IAAI,GAC1B,OAAO;IACL,MAAM,UAAW,MAAM,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAChC,MAAM,SAAS,SAAS,MAAM,MAAM,UAAU,UAAU;IACxD,MAAM,cAAc,SAAS,MAAM,MAAM;IAEzC,IAAI,KAAK,UAAU,WAAW,OAAO,CAAC,gBAAgB,aAAa;QACjE,eAAe;QACf,IAAI;YACF,MAAM,CAAA,GAAA,sHAAA,CAAA,SAAM,AAAD,EAAE;YACb,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,mIAAA,CAAA,gBAAa;YAE/B,uCAAmC;;YAEnC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,eAAe;QACjB;IACF,OAAO,IAAI,KAAK,UAAU,WAAW,KAAK;QACxC,gDAAgD;QAChD,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,UAAU,MAAM;IACzC;IACA,OAAO,CAAA,GAAA,mHAAA,CAAA,YAAS,AAAD,EAAE,KAAK,UAAU,MAAM;AACxC;AAGK,SAAS,IAAU,GAAW,EAAE,MAAU;IAC/C,OAAO,KAAK;QACV,QAAQ;QACR;QACA;IACF;AACF;AAEO,SAAS,KAAc,GAAW,EAAE,IAAO,EAAE,MAAU;IAC5D,OAAO,KAAK;QACV,QAAQ;QACR;QACA;QACA;IACF;AACF;AAEO,SAAS,SAAkB,GAAW,EAAE,IAAO,EAAE,MAAU;IAChE,OAAO,KAAK;QACV,QAAQ;QACR;QACA;QACA;QACA,SAAS;YAAE,gBAAgB;QAAsB;IACnD;AACF;AACO,SAAS,IAAa,GAAW,EAAE,IAAO,EAAE,MAAU;IAC3D,OAAO,KAAK;QACV,QAAQ;QACR;QACA;QACA;IACF;AACF;AAEO,SAAS,MAAe,GAAW,EAAE,IAAO,EAAE,MAAU;IAC7D,OAAO,KAAK;QACV,QAAQ;QACR;QACA;QACA;IACF;AACF;AACO,SAAS,OAAa,GAAW,EAAE,MAAU;IAClD,OAAO,KAAK;QACV,QAAQ;QACR;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1553, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1559, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/authServices.ts"], "sourcesContent": ["import endpoint from \"@/constants/endpoint\";\nimport { IForgotPassword, ILogin, IResendOTP, IResetPassword, IVerifyOTP, UserPermissionsResponse } from \"@/interfaces/authInterfaces\";\nimport * as http from \"@/utils/http\";\nimport { ApiResponse, IApiResponseCommonInterface } from \"@/interfaces/commonInterfaces\";\nimport { AxiosResponse } from \"axios\";\n\n// Using UserPermissionsResponse interface from authInterfaces.ts\n\nexport const logIn = (data: ILogin): Promise<ApiResponse> => {\n  return http.post(endpoint.auth.SIGNIN, data);\n};\n\nexport const verifyOTP = (data: IVerifyOTP): Promise<IApiResponseCommonInterface<string>> => {\n  return http.post(endpoint.auth.VERIFY_OTP, data);\n};\n\nexport const resendOTP = (data: IResendOTP): Promise<ApiResponse<null>> => {\n  return http.post(endpoint.auth.RESEND_OTP, data);\n};\n\nexport const forgotPassword = (data: IForgotPassword): Promise<ApiResponse<null>> => {\n  return http.post(endpoint.auth.FORGOT_PASSWORD, data);\n};\n\nexport const resetPassword = (data: IResetPassword): Promise<ApiResponse<null>> => {\n  return http.post(endpoint.auth.RESET_PASSWORD, data);\n};\n\nexport const deleteSession = (userId?: number): Promise<ApiResponse | AxiosResponse> => {\n  return http.remove(`${endpoint.auth.DELETE_SESSION}/${userId}`);\n};\n\nexport const updateTimezone = (data: { timezone: string }): Promise<ApiResponse<null>> => {\n  return http.post(endpoint.auth.UPDATE_TIMEZONE, data);\n};\n\nexport const getUserPermissions = (): Promise<IApiResponseCommonInterface<UserPermissionsResponse>> => {\n  return http.get(endpoint.roles.USER_PERMISSIONS);\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAEA;;;AAMO,MAAM,QAAQ,CAAC;IACpB,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,IAAI,CAAC,MAAM,EAAE;AACzC;AAEO,MAAM,YAAY,CAAC;IACxB,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,IAAI,CAAC,UAAU,EAAE;AAC7C;AAEO,MAAM,YAAY,CAAC;IACxB,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,IAAI,CAAC,UAAU,EAAE;AAC7C;AAEO,MAAM,iBAAiB,CAAC;IAC7B,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,IAAI,CAAC,eAAe,EAAE;AAClD;AAEO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,IAAI,CAAC,cAAc,EAAE;AACjD;AAEO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,CAAA,GAAA,oHAAA,CAAA,SAAW,AAAD,EAAE,GAAG,4HAAA,CAAA,UAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,QAAQ;AAChE;AAEO,MAAM,iBAAiB,CAAC;IAC7B,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,IAAI,CAAC,eAAe,EAAE;AAClD;AAEO,MAAM,qBAAqB;IAChC,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,gBAAgB;AACjD", "debugId": null}}, {"offset": {"line": 1597, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1603, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/commonService.ts"], "sourcesContent": ["import * as http from \"@/utils/http\";\nimport endpoint from \"@/constants/endpoint\";\nimport { ApiResponse, FilePath } from \"@/interfaces/commonInterfaces\";\n\nexport const removeAttachmentsFromS3 = (data: { fileUrlArray: string }): Promise<ApiResponse<null>> => {\n  return http.post(endpoint.common.REMOVE_ATTACHMENTS_FROM_S3, data);\n};\n\nexport const getSignedUrl = (data: FilePath): Promise<ApiResponse<string | null>> => {\n  return http.post(endpoint.common.GENERATE_PRESIGNED_URL, data);\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,MAAM,0BAA0B,CAAC;IACtC,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,0BAA0B,EAAE;AAC/D;AAEO,MAAM,eAAe,CAAC;IAC3B,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,sBAAsB,EAAE;AAC3D", "debugId": null}}, {"offset": {"line": 1617, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1628, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/Profile.svg.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 24, height: 24, blurDataURL: null, blurWidth: 0, blurHeight: 0 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,8HAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAM,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 1641, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1647, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/dataSecurityIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction dataSecurityIcon() {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"30\" height=\"30\" viewBox=\"0 0 43 42\" fill=\"none\">\n      <circle cx=\"21.5\" cy=\"21\" r=\"21\" fill=\"url(#paint0_linear_9593_1613)\" />\n      <path\n        d=\"M21.5091 9.28711H21.4886C18.2484 9.93022 15.0058 10.5721 11.7656 11.2139C11.7927 14.3154 11.8183 17.4181 11.8451 20.5211C11.8835 25.2709 14.6681 29.5713 18.9852 31.5523C19.8209 31.9347 20.6541 32.3187 21.4886 32.7014V32.7102C21.4924 32.709 21.4961 32.7064 21.499 32.7052C21.5015 32.7064 21.5053 32.709 21.5094 32.7102V32.7014C22.3439 32.3187 23.1771 31.935 24.0128 31.5523C28.3298 29.5716 31.1144 25.2709 31.1529 20.5211C31.1797 17.4184 31.2055 14.3154 31.2323 11.2139C27.9919 10.5721 24.7492 9.93022 21.5091 9.28711ZM29.4181 20.6065C29.3856 24.503 27.1019 28.0303 23.5604 29.6558C22.8757 29.9694 22.1919 30.2841 21.5072 30.5974V30.605C21.5043 30.604 21.5015 30.6021 21.4987 30.6012C21.4968 30.6021 21.4939 30.604 21.4911 30.605V30.5974C20.8064 30.2837 20.1226 29.9691 19.4379 29.6558C15.8964 28.0303 13.6127 24.503 13.5802 20.6065C13.5581 18.0621 13.537 15.5171 13.515 12.9727C16.1735 12.4462 18.8326 11.9198 21.4911 11.3924H21.5075C24.166 11.9198 26.8251 12.4462 29.4837 12.9727C29.4616 15.5171 29.4405 18.0621 29.4184 20.6065H29.4181Z\"\n        fill=\"white\"\n      />\n      <path\n        d=\"M25.0804 18.5382H24.734V17.2775C24.734 15.5752 23.3538 14.1953 21.6518 14.1953H21.3743C19.672 14.1953 18.2921 15.5752 18.2921 17.2775V18.5382H17.9457C17.6619 18.5382 17.4321 18.768 17.4321 19.0517V24.4369C17.4321 24.7206 17.6619 24.9517 17.9457 24.9517H25.0807C25.3645 24.9517 25.5955 24.7206 25.5955 24.4369V19.0517C25.5955 18.768 25.3645 18.5382 25.0807 18.5382H25.0804ZM21.7965 21.7077V23.3868C21.7965 23.4804 21.7195 23.5576 21.6243 23.5576H21.3996C21.3059 23.5576 21.2287 23.4807 21.2287 23.3868V21.7077C20.9525 21.5961 20.7561 21.3253 20.7561 21.0069C20.7561 20.5949 21.0875 20.2598 21.4982 20.2535C21.5033 20.2522 21.5074 20.2522 21.5124 20.2522C21.9282 20.2522 22.2671 20.5899 22.2671 21.0069C22.2671 21.3253 22.072 21.5961 21.7961 21.7077H21.7965ZM23.7554 18.5382H19.2136V17.1672C19.2136 15.967 20.1855 14.9938 21.3869 14.9938H21.5808C22.7822 14.9938 23.7554 15.967 23.7554 17.1672V18.5382Z\"\n        fill=\"white\"\n      />\n      <defs>\n        <linearGradient id=\"paint0_linear_9593_1613\" x1=\"-2.3\" y1=\"17.5\" x2=\"29.5828\" y2=\"-6.01022\" gradientUnits=\"userSpaceOnUse\">\n          <stop stopColor=\"#74A8FF\" />\n          <stop offset=\"0.474301\" stopColor=\"#AACAFF\" />\n          <stop offset=\"1\" stopColor=\"#5D86CC\" />\n        </linearGradient>\n      </defs>\n    </svg>\n  );\n}\n\nexport default dataSecurityIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS;IACP,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;;0BACtF,8OAAC;gBAAO,IAAG;gBAAO,IAAG;gBAAK,GAAE;gBAAK,MAAK;;;;;;0BACtC,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;0BACC,cAAA,8OAAC;oBAAe,IAAG;oBAA0B,IAAG;oBAAO,IAAG;oBAAO,IAAG;oBAAU,IAAG;oBAAW,eAAc;;sCACxG,8OAAC;4BAAK,WAAU;;;;;;sCAChB,8OAAC;4BAAK,QAAO;4BAAW,WAAU;;;;;;sCAClC,8OAAC;4BAAK,QAAO;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKrC;uCAEe", "debugId": null}}, {"offset": {"line": 1737, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1743, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/constants/routes.ts"], "sourcesContent": ["const ROUTES = {\n  LOGIN: \"/login\",\n  FORGOT_PASSWORD: \"/forgot-password\",\n  VERIFY: \"/verify\",\n  RESET_PASSWORD: \"/reset-password\",\n  CANDIDATE_ASSESSMENT: \"/candidate-assessment\",\n  DASHBOARD: \"/dashboard\",\n  HOME: \"/\",\n  PROFILE: {\n    MY_PROFILE: \"/my-profile\",\n  },\n  JOBS: {\n    CAREER_BASED_SKILLS: \"/career-based-skills\",\n    ROLE_BASED_SKILLS: \"/role-based-skills\",\n    CULTURE_BASED_SKILLS: \"/culture-based-skills\",\n    GENERATE_JOB: \"/generate-job\",\n    EDIT_SKILLS: \"/edit-skills\",\n    HIRING_TYPE: \"/hiring-type\",\n    JOB_EDITOR: \"/job-editor\",\n    ACTIVE_JOBS: \"/active-jobs\",\n    CANDIDATE_PROFILE: \"/candidate-profile\",\n    ARCHIVE: \"/archive\",\n  },\n  SCREEN_RESUME: {\n    MANUAL_CANDIDATE_UPLOAD: \"/manual-upload-resume\",\n    CANDIDATE_QUALIFICATION: \"/candidate-qualification\",\n    CANDIDATE_LIST: \"/candidates-list\",\n    CANDIDATES: \"/candidates\",\n  },\n  INTERVIEW: {\n    ADD_CANDIDATE_INFO: \"/additional-submission\",\n    SCHEDULE_INTERVIEW: \"/schedule-interview\",\n    PRE_INTERVIEW_QUESTIONS_OVERVIEW: \"/pre-interview-questions-overview\",\n    INTERVIEW_QUESTION: \"/interview-question\",\n    CALENDAR: \"/calendar\",\n    INTERVIEW_SUMMARY: \"/interview-summary\",\n  },\n\n  ROLE_EMPLOYEES: {\n    USER_ROLE: \"/user-roles\",\n    EMPLOYEE_MANAGEMENT: \"/employee-management\",\n    EMPLOYEE_MANAGEMENT_DETAIL: \"/employee-management-detail\",\n    ADD_EMPLOYEE: \"/add-employees\",\n    ADD_DEPARTMENT: \"/add-department\",\n  },\n\n  FINAL_ASSESSMENT: {\n    FINAL_ASSESSMENT: \"/final-assessment\",\n  },\n};\n\nexport const BEFORE_LOGIN_ROUTES = [ROUTES.LOGIN, ROUTES.FORGOT_PASSWORD, ROUTES.VERIFY, ROUTES.RESET_PASSWORD, ROUTES.CANDIDATE_ASSESSMENT];\n\nexport default ROUTES;\n"], "names": [], "mappings": ";;;;AAAA,MAAM,SAAS;IACb,OAAO;IACP,iBAAiB;IACjB,QAAQ;IACR,gBAAgB;IAChB,sBAAsB;IACtB,WAAW;IACX,MAAM;IACN,SAAS;QACP,YAAY;IACd;IACA,MAAM;QACJ,qBAAqB;QACrB,mBAAmB;QACnB,sBAAsB;QACtB,cAAc;QACd,aAAa;QACb,aAAa;QACb,YAAY;QACZ,aAAa;QACb,mBAAmB;QACnB,SAAS;IACX;IACA,eAAe;QACb,yBAAyB;QACzB,yBAAyB;QACzB,gBAAgB;QAChB,YAAY;IACd;IACA,WAAW;QACT,oBAAoB;QACpB,oBAAoB;QACpB,kCAAkC;QAClC,oBAAoB;QACpB,UAAU;QACV,mBAAmB;IACrB;IAEA,gBAAgB;QACd,WAAW;QACX,qBAAqB;QACrB,4BAA4B;QAC5B,cAAc;QACd,gBAAgB;IAClB;IAEA,kBAAkB;QAChB,kBAAkB;IACpB;AACF;AAEO,MAAM,sBAAsB;IAAC,OAAO,KAAK;IAAE,OAAO,eAAe;IAAE,OAAO,MAAM;IAAE,OAAO,cAAc;IAAE,OAAO,oBAAoB;CAAC;uCAE7H", "debugId": null}}, {"offset": {"line": 1803, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1809, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/loader/Loader.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useTranslations } from \"next-intl\";\n\nconst Loader = ({ className }: { className?: string }) => {\n  const t = useTranslations();\n  return (\n    <div className={`spinner-border ${className}`} role=\"status\">\n      <span className=\"visually-hidden\">{t(\"loading\")}</span>\n    </div>\n  );\n};\n\nexport default Loader;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,SAAS,CAAC,EAAE,SAAS,EAA0B;IACnD,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD;IACxB,qBACE,8OAAC;QAAI,WAAW,CAAC,eAAe,EAAE,WAAW;QAAE,MAAK;kBAClD,cAAA,8OAAC;YAAK,WAAU;sBAAmB,EAAE;;;;;;;;;;;AAG3C;uCAEe", "debugId": null}}, {"offset": {"line": 1837, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1843, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/Button.tsx"], "sourcesContent": ["import React, { ButtonHTMLAttributes, DetailedHTMLP<PERSON>, MouseEvent, ReactNode } from \"react\";\nimport Loader from \"@/components/loader/Loader\";\n\ninterface Props extends DetailedHTMLProps<ButtonHTMLAttributes<HTMLButtonElement>, HTMLButtonElement> {\n  onClick?: (event: MouseEvent<HTMLButtonElement>) => void;\n  disabled?: boolean;\n  children?: ReactNode;\n  loading?: boolean;\n}\n\nconst Button = ({ className, type, disabled = false, onClick, children, loading }: Props) => (\n  <button\n    type={type}\n    className={`theme-btn ${className}`}\n    onClick={(e) => {\n      if (onClick) {\n        onClick(e as unknown as MouseEvent<HTMLButtonElement>);\n      }\n    }}\n    disabled={disabled}\n    aria-label=\"\"\n  >\n    {children}\n    {loading ? <Loader /> : null}\n  </button>\n);\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AACA;;;AASA,MAAM,SAAS,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,WAAW,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAS,iBACtF,8OAAC;QACC,MAAM;QACN,WAAW,CAAC,UAAU,EAAE,WAAW;QACnC,SAAS,CAAC;YACR,IAAI,SAAS;gBACX,QAAQ;YACV;QACF;QACA,UAAU;QACV,cAAW;;YAEV;YACA,wBAAU,8OAAC,sIAAA,CAAA,UAAM;;;;uBAAM;;;;;;;uCAIb", "debugId": null}}, {"offset": {"line": 1874, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1880, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/header/Header.tsx"], "sourcesContent": ["\"use client\";\nimport React, { useCallback, useEffect, useState } from \"react\";\nimport Link from \"next/link\";\nimport Image from \"next/image\";\nimport { useSelector } from \"react-redux\";\nimport { AuthState } from \"@/redux/slices/authSlice\";\nimport { useTranslations } from \"next-intl\";\n\nimport { syncReduxStateToCookies } from \"@/utils/syncReduxToCookies\";\nimport Logo from \"../../../public/assets/images/logo.svg\";\nimport downArrow from \"../../../public/assets/images/down-arrow.svg\";\nimport User from \"../../../public/assets/images/user.png\";\nimport styles from \"@/styles/header.module.scss\";\nimport NotificationIcon from \"../svgComponents/Notification\";\nimport { logout } from \"@/utils/helper\";\nimport { usePathname, useRouter } from \"next/navigation\";\nimport { useDispatch } from \"react-redux\";\nimport { selectProfileData, setPermissions } from \"@/redux/slices/authSlice\";\nimport { getUserPermissions } from \"@/services/authServices\";\nimport Profile from \"../../../public/assets/images/Profile.svg\";\n\n// Interface definitions moved to authServices.ts\nimport DataSecurityIcon from \"../svgComponents/dataSecurityIcon\";\nimport ROUTES from \"@/constants/routes\";\nimport Button from \"../formElements/Button\";\nimport { IUserData } from \"@/interfaces/authInterfaces\";\n\nconst Header = () => {\n  const [dropdown, SetDropdown] = useState(false);\n  const userProfile: IUserData | null = useSelector(selectProfileData);\n\n  const path = usePathname();\n  const dispatch = useDispatch();\n  const authData = useSelector((state: { auth: AuthState }) => state.auth.authData);\n  const t = useTranslations(\"header\");\n  const tCommon = useTranslations(\"common\");\n  const pathname = usePathname();\n  const dropdownRef = React.useRef<HTMLDivElement>(null);\n\n  const navigate = useRouter();\n  const [showNotifications, setShowNotifications] = useState(false);\n\n  // Handle clicks outside of dropdown to close it\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        SetDropdown(false);\n      }\n    };\n\n    // Add event listener when dropdown is open\n    if (dropdown) {\n      document.addEventListener(\"mousedown\", handleClickOutside);\n    }\n\n    // Clean up event listener\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside);\n    };\n  }, [dropdown]);\n\n  // Toggle dropdown visibility\n  const MenuDropdown = () => {\n    SetDropdown(!dropdown);\n  };\n\n  // Function to fetch permissions using the authServices\n  const fetchPermissions = useCallback(async () => {\n    try {\n      const response = await getUserPermissions();\n\n      // Only update Redux store when success is true\n      if (response.data?.success) {\n        dispatch(setPermissions(response.data.data.rolePermissions));\n        // Sync Redux state to cookies after updating permissions\n        syncReduxStateToCookies(response.data.data.rolePermissions, true);\n      } else {\n        console.log(\"Permission fetch unsuccessful:\", response.data?.message);\n      }\n    } catch (error) {\n      console.error(\"Error fetching permissions:\", error);\n    }\n  }, [path, dispatch]);\n\n  // when someone manually removes localStor\n\n  // useEffect(() => {\n  //   logoutUser();\n  // }, []);\n\n  // Sync Redux state to cookies after mounting component\n  useEffect(() => {\n    syncReduxStateToCookies();\n  }, []);\n\n  useEffect(() => {\n    // Check if this is first mount or a genuine route change\n    fetchPermissions();\n  }, [path, dispatch, fetchPermissions]);\n\n  /**\n   * Logs out the user if the access token is invalid.\n   * If the access token is invalid, it logs out the user and shows a toast message.\n   */\n\n  // const logoutUser = async () => {\n  //   const token = getAccessToken();\n  //   if (!token) {\n  //     onHandleLogout();\n  //     toast.dismiss();\n  //     toastMessageError(t(\"session_expired\"));\n  //   }\n  // };\n\n  const onHandleLogout = async () => {\n    await logout(authData?.id);\n\n    if (typeof window !== \"undefined\") {\n      window.location.reload();\n    }\n  };\n\n  return (\n    <>\n      <header\n        className={styles.header}\n        // className={`${styles.header} ${isVisible ? \"\" : `${styles.hidden}`}`}\n      >\n        <nav className=\"navbar navbar-expand-sm\">\n          <div className=\"container\">\n            <Link className=\"navbar-brand\" href={ROUTES.HOME}>\n              <Image src={Logo} alt=\"logo\" width={640} height={320} className={styles.logo} />\n            </Link>\n            {/* <Button className=\"navbar-toggler\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#collapsibleNavbar\">\n              <span className=\"navbar-toggler-icon\"></span>\n            </Button> */}\n            <div className={`collapse navbar-collapse justify-content-end ${styles.navbar_content}`} id=\"collapsibleNavbar\">\n              <ul className={`navbar-nav ${styles.navbar_links}`}>\n                <li className=\"nav-item\">\n                  <Link\n                    className={`nav-link ${pathname === ROUTES.JOBS.GENERATE_JOB || pathname === ROUTES.JOBS.CAREER_BASED_SKILLS || pathname === ROUTES.JOBS.CULTURE_BASED_SKILLS || pathname === ROUTES.JOBS.ROLE_BASED_SKILLS || pathname === ROUTES.JOBS.EDIT_SKILLS || pathname === ROUTES.JOBS.JOB_EDITOR || pathname === ROUTES.JOBS.HIRING_TYPE ? styles.active : \"\"}`}\n                    href={ROUTES.JOBS.HIRING_TYPE}\n                  >\n                    {t(\"job_requirement_generations\")}\n                  </Link>\n                </li>\n                <li className=\"nav-item\">\n                  <Link\n                    className={`nav-link ${pathname === ROUTES.JOBS.ACTIVE_JOBS || pathname?.startsWith(ROUTES.SCREEN_RESUME.MANUAL_CANDIDATE_UPLOAD) || pathname?.startsWith(ROUTES.SCREEN_RESUME.CANDIDATE_QUALIFICATION) ? styles.active : \"\"}`}\n                    href={ROUTES.JOBS.ACTIVE_JOBS}\n                  >\n                    {t(\"resume_screening\")}\n                  </Link>\n                </li>\n                <li className=\"nav-item\">\n                  <Link className=\"nav-link\" href=\"#\">\n                    {t(\"conduct_interview\")}\n                  </Link>\n                </li>\n                <li className=\"nav-item\">\n                  <Link\n                    className={`nav-link ${pathname === ROUTES.DASHBOARD || pathname === ROUTES.SCREEN_RESUME.CANDIDATES || pathname === ROUTES.JOBS.ARCHIVE ? styles.active : \"\"}`}\n                    href={ROUTES.DASHBOARD}\n                  >\n                    {tCommon(\"hm_dashboard\")}\n                  </Link>\n                </li>\n              </ul>\n\n              <div className={styles.header_right}>\n                <NotificationIcon hasNotification={true} onClick={() => setShowNotifications(!showNotifications)} />\n                <div className={`dropdown ${styles.user_drop}`} ref={dropdownRef}>\n                  <button type=\"button\" className={`dropdown-toggle ${styles.user_drop_btn}`} data-bs-toggle=\"dropdown\" onClick={MenuDropdown}>\n                    <div className={`${styles.circle_img}`}>\n                      <Image src={userProfile?.image || User} alt=\"Profile\" width={100} height={100} />\n                    </div>\n                    <div className={styles.admin_info}>\n                      <h5>{`${userProfile?.first_name}`}</h5>\n                    </div>\n                    <Image src={downArrow} alt=\"downArrow\" style={{ rotate: `${dropdown ? \"180deg\" : \"0deg\"}` }} />\n                  </button>\n                  {dropdown && (\n                    <ul className={styles.dropdown_menu}>\n                      <li>\n                        <Image src={Profile} alt=\"userPlaceholder\" />\n                        <span\n                          onClick={() => {\n                            navigate.push(ROUTES.PROFILE.MY_PROFILE);\n                            SetDropdown(false);\n                          }}\n                        >\n                          {t(\"my_profile\")}\n                        </span>\n                      </li>\n                      <li>\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <path\n                            d=\"M14.9453 1.25C13.5778 1.24998 12.4754 1.24996 11.6085 1.36652C10.7084 1.48754 9.95049 1.74643 9.34858 2.34835C8.82364 2.87328 8.5584 3.51836 8.41917 4.27635C8.28388 5.01291 8.25799 5.9143 8.25196 6.99583C8.24966 7.41003 8.58358 7.74768 8.99779 7.74999C9.412 7.7523 9.74965 7.41838 9.75195 7.00418C9.75804 5.91068 9.78644 5.1356 9.89449 4.54735C9.9986 3.98054 10.1658 3.65246 10.4092 3.40901C10.686 3.13225 11.0746 2.9518 11.8083 2.85315C12.5637 2.75159 13.5648 2.75 15.0002 2.75H16.0002C17.4356 2.75 18.4367 2.75159 19.1921 2.85315C19.9259 2.9518 20.3144 3.13225 20.5912 3.40901C20.868 3.68577 21.0484 4.07435 21.1471 4.80812C21.2486 5.56347 21.2502 6.56459 21.2502 8V16C21.2502 17.4354 21.2486 18.4365 21.1471 19.1919C21.0484 19.9257 20.868 20.3142 20.5912 20.591C20.3144 20.8678 19.9259 21.0482 19.1921 21.1469C18.4367 21.2484 17.4356 21.25 16.0002 21.25H15.0002C13.5648 21.25 12.5637 21.2484 11.8083 21.1469C11.0746 21.0482 10.686 20.8678 10.4092 20.591C10.1658 20.3475 9.9986 20.0195 9.89449 19.4527C9.78644 18.8644 9.75804 18.0893 9.75195 16.9958C9.74965 16.5816 9.412 16.2477 8.99779 16.25C8.58358 16.2523 8.24966 16.59 8.25196 17.0042C8.25799 18.0857 8.28388 18.9871 8.41917 19.7236C8.5584 20.4816 8.82364 21.1267 9.34858 21.6517C9.95049 22.2536 10.7084 22.5125 11.6085 22.6335C12.4754 22.75 13.5778 22.75 14.9453 22.75H16.0551C17.4227 22.75 18.525 22.75 19.392 22.6335C20.2921 22.5125 21.0499 22.2536 21.6519 21.6517C22.2538 21.0497 22.5127 20.2919 22.6337 19.3918C22.7503 18.5248 22.7502 17.4225 22.7502 16.0549V7.94513C22.7502 6.57754 22.7503 5.47522 22.6337 4.60825C22.5127 3.70814 22.2538 2.95027 21.6519 2.34835C21.0499 1.74643 20.2921 1.48754 19.392 1.36652C18.525 1.24996 17.4227 1.24998 16.0551 1.25H14.9453Z\"\n                            fill=\"#191919\"\n                          />\n                          <path\n                            d=\"M15 11.25C15.4142 11.25 15.75 11.5858 15.75 12C15.75 12.4142 15.4142 12.75 15 12.75H4.02744L5.98809 14.4306C6.30259 14.7001 6.33901 15.1736 6.06944 15.4881C5.79988 15.8026 5.3264 15.839 5.01191 15.5694L1.51191 12.5694C1.34567 12.427 1.25 12.2189 1.25 12C1.25 11.7811 1.34567 11.573 1.51191 11.4306L5.01191 8.43056C5.3264 8.16099 5.79988 8.19741 6.06944 8.51191C6.33901 8.8264 6.30259 9.29988 5.98809 9.56944L4.02744 11.25H15Z\"\n                            fill=\"#191919\"\n                          />\n                        </svg>\n                        <span onClick={() => onHandleLogout()}>Logout</span>\n                      </li>\n                    </ul>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n        </nav>\n      </header>\n      {showNotifications ? (\n        <div className=\"notifications\">\n          <div className=\"header-content\">\n            <h3>Notifications</h3>\n            <Button className=\"clear-btn p-0\">Clear All</Button>\n          </div>\n          <div className=\"read-btns\">\n            <Button className=\"primary-btn\">All</Button>\n            <Button className=\"grey-btn\">Unread</Button>\n          </div>\n          <div className=\"notification-wrapper\">\n            <div className=\"notification-item unread\">\n              <h4>Upcoming Interview for Operations Admin</h4>\n              <p>You have an interview scheduled on May 24, 2025 at 10:00 AM. </p>\n              <p className=\"time\">May 22, 2025 | 03:36 PM</p>\n            </div>\n            <div className=\"notification-item\">\n              <h4>Upcoming Interview for Operations Admin</h4>\n              <p>You have an interview scheduled on May 24, 2025 at 10:00 AM. </p>\n              <p className=\"time\">May 22, 2025 | 03:36 PM</p>\n            </div>\n            <div className=\"notification-item\">\n              <h4>Upcoming Interview for Operations Admin</h4>\n              <p>You have an interview scheduled on May 24, 2025 at 10:00 AM. </p>\n              <p className=\"time\">May 22, 2025 | 03:36 PM</p>\n            </div>\n          </div>\n        </div>\n      ) : null}\n\n      {/* common pages information box for  Job Requirement Generation page */}\n      {pathname === ROUTES.JOBS.GENERATE_JOB && (\n        <div className=\"information-box\">\n          <DataSecurityIcon />\n          <p>We prioritize your data’s security. With encryption at every step, your privacy is secure and protected.</p>\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA,iDAAiD;AACjD;AACA;AACA;AAxBA;;;;;;;;;;;;;;;;;;;;;;AA2BA,MAAM,SAAS;IACb,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,cAAgC,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,mIAAA,CAAA,oBAAiB;IAEnE,MAAM,OAAO,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAA+B,MAAM,IAAI,CAAC,QAAQ;IAChF,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,UAAU,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAChC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,cAAc,qMAAA,CAAA,UAAK,CAAC,MAAM,CAAiB;IAEjD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACzB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,YAAY;YACd;QACF;QAEA,2CAA2C;QAC3C,IAAI,UAAU;YACZ,SAAS,gBAAgB,CAAC,aAAa;QACzC;QAEA,0BAA0B;QAC1B,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG;QAAC;KAAS;IAEb,6BAA6B;IAC7B,MAAM,eAAe;QACnB,YAAY,CAAC;IACf;IAEA,uDAAuD;IACvD,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnC,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD;YAExC,+CAA+C;YAC/C,IAAI,SAAS,IAAI,EAAE,SAAS;gBAC1B,SAAS,CAAA,GAAA,mIAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,IAAI,CAAC,IAAI,CAAC,eAAe;gBAC1D,yDAAyD;gBACzD,CAAA,GAAA,kIAAA,CAAA,0BAAuB,AAAD,EAAE,SAAS,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YAC9D,OAAO;gBACL,QAAQ,GAAG,CAAC,kCAAkC,SAAS,IAAI,EAAE;YAC/D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF,GAAG;QAAC;QAAM;KAAS;IAEnB,0CAA0C;IAE1C,oBAAoB;IACpB,kBAAkB;IAClB,UAAU;IAEV,uDAAuD;IACvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,CAAA,GAAA,kIAAA,CAAA,0BAAuB,AAAD;IACxB,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,yDAAyD;QACzD;IACF,GAAG;QAAC;QAAM;QAAU;KAAiB;IAErC;;;GAGC,GAED,mCAAmC;IACnC,oCAAoC;IACpC,kBAAkB;IAClB,wBAAwB;IACxB,uBAAuB;IACvB,+CAA+C;IAC/C,MAAM;IACN,KAAK;IAEL,MAAM,iBAAiB;QACrB,MAAM,CAAA,GAAA,sHAAA,CAAA,SAAM,AAAD,EAAE,UAAU;QAEvB,uCAAmC;;QAEnC;IACF;IAEA,qBACE;;0BACE,8OAAC;gBACC,WAAW,qJAAA,CAAA,UAAM,CAAC,MAAM;0BAGxB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,WAAU;gCAAe,MAAM,0HAAA,CAAA,UAAM,CAAC,IAAI;0CAC9C,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCAAC,KAAK,oSAAA,CAAA,UAAI;oCAAE,KAAI;oCAAO,OAAO;oCAAK,QAAQ;oCAAK,WAAW,qJAAA,CAAA,UAAM,CAAC,IAAI;;;;;;;;;;;0CAK9E,8OAAC;gCAAI,WAAW,CAAC,6CAA6C,EAAE,qJAAA,CAAA,UAAM,CAAC,cAAc,EAAE;gCAAE,IAAG;;kDAC1F,8OAAC;wCAAG,WAAW,CAAC,WAAW,EAAE,qJAAA,CAAA,UAAM,CAAC,YAAY,EAAE;;0DAChD,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,WAAW,CAAC,SAAS,EAAE,aAAa,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,YAAY,IAAI,aAAa,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,mBAAmB,IAAI,aAAa,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,oBAAoB,IAAI,aAAa,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,iBAAiB,IAAI,aAAa,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,WAAW,IAAI,aAAa,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU,IAAI,aAAa,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,WAAW,GAAG,qJAAA,CAAA,UAAM,CAAC,MAAM,GAAG,IAAI;oDACzV,MAAM,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,WAAW;8DAE5B,EAAE;;;;;;;;;;;0DAGP,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,WAAW,CAAC,SAAS,EAAE,aAAa,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,WAAW,IAAI,UAAU,WAAW,0HAAA,CAAA,UAAM,CAAC,aAAa,CAAC,uBAAuB,KAAK,UAAU,WAAW,0HAAA,CAAA,UAAM,CAAC,aAAa,CAAC,uBAAuB,IAAI,qJAAA,CAAA,UAAM,CAAC,MAAM,GAAG,IAAI;oDAC9N,MAAM,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,WAAW;8DAE5B,EAAE;;;;;;;;;;;0DAGP,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,WAAU;oDAAW,MAAK;8DAC7B,EAAE;;;;;;;;;;;0DAGP,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,WAAW,CAAC,SAAS,EAAE,aAAa,0HAAA,CAAA,UAAM,CAAC,SAAS,IAAI,aAAa,0HAAA,CAAA,UAAM,CAAC,aAAa,CAAC,UAAU,IAAI,aAAa,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,OAAO,GAAG,qJAAA,CAAA,UAAM,CAAC,MAAM,GAAG,IAAI;oDAC/J,MAAM,0HAAA,CAAA,UAAM,CAAC,SAAS;8DAErB,QAAQ;;;;;;;;;;;;;;;;;kDAKf,8OAAC;wCAAI,WAAW,qJAAA,CAAA,UAAM,CAAC,YAAY;;0DACjC,8OAAC,mJAAA,CAAA,UAAgB;gDAAC,iBAAiB;gDAAM,SAAS,IAAM,qBAAqB,CAAC;;;;;;0DAC9E,8OAAC;gDAAI,WAAW,CAAC,SAAS,EAAE,qJAAA,CAAA,UAAM,CAAC,SAAS,EAAE;gDAAE,KAAK;;kEACnD,8OAAC;wDAAO,MAAK;wDAAS,WAAW,CAAC,gBAAgB,EAAE,qJAAA,CAAA,UAAM,CAAC,aAAa,EAAE;wDAAE,kBAAe;wDAAW,SAAS;;0EAC7G,8OAAC;gEAAI,WAAW,GAAG,qJAAA,CAAA,UAAM,CAAC,UAAU,EAAE;0EACpC,cAAA,8OAAC,6HAAA,CAAA,UAAK;oEAAC,KAAK,aAAa,SAAS,oSAAA,CAAA,UAAI;oEAAE,KAAI;oEAAU,OAAO;oEAAK,QAAQ;;;;;;;;;;;0EAE5E,8OAAC;gEAAI,WAAW,qJAAA,CAAA,UAAM,CAAC,UAAU;0EAC/B,cAAA,8OAAC;8EAAI,GAAG,aAAa,YAAY;;;;;;;;;;;0EAEnC,8OAAC,6HAAA,CAAA,UAAK;gEAAC,KAAK,sTAAA,CAAA,UAAS;gEAAE,KAAI;gEAAY,OAAO;oEAAE,QAAQ,GAAG,WAAW,WAAW,QAAQ;gEAAC;;;;;;;;;;;;oDAE3F,0BACC,8OAAC;wDAAG,WAAW,qJAAA,CAAA,UAAM,CAAC,aAAa;;0EACjC,8OAAC;;kFACC,8OAAC,6HAAA,CAAA,UAAK;wEAAC,KAAK,0SAAA,CAAA,UAAO;wEAAE,KAAI;;;;;;kFACzB,8OAAC;wEACC,SAAS;4EACP,SAAS,IAAI,CAAC,0HAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;4EACvC,YAAY;wEACd;kFAEC,EAAE;;;;;;;;;;;;0EAGP,8OAAC;;kFACC,8OAAC;wEAAI,OAAM;wEAA6B,OAAM;wEAAK,QAAO;wEAAK,SAAQ;wEAAY,MAAK;;0FACtF,8OAAC;gFACC,GAAE;gFACF,MAAK;;;;;;0FAEP,8OAAC;gFACC,GAAE;gFACF,MAAK;;;;;;;;;;;;kFAGT,8OAAC;wEAAK,SAAS,IAAM;kFAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUxD,kCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAG;;;;;;0CACJ,8OAAC,4IAAA,CAAA,UAAM;gCAAC,WAAU;0CAAgB;;;;;;;;;;;;kCAEpC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4IAAA,CAAA,UAAM;gCAAC,WAAU;0CAAc;;;;;;0CAChC,8OAAC,4IAAA,CAAA,UAAM;gCAAC,WAAU;0CAAW;;;;;;;;;;;;kCAE/B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAE;;;;;;kDACH,8OAAC;wCAAE,WAAU;kDAAO;;;;;;;;;;;;0CAEtB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAE;;;;;;kDACH,8OAAC;wCAAE,WAAU;kDAAO;;;;;;;;;;;;0CAEtB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAE;;;;;;kDACH,8OAAC;wCAAE,WAAU;kDAAO;;;;;;;;;;;;;;;;;;;;;;;uBAIxB;YAGH,aAAa,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,YAAY,kBACpC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,uJAAA,CAAA,UAAgB;;;;;kCACjB,8OAAC;kCAAE;;;;;;;;;;;;;;AAKb;uCAEe", "debugId": null}}, {"offset": {"line": 2486, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2492, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/header/HeaderWrapper.tsx"], "sourcesContent": ["\"use client\";\nimport { usePathname } from \"next/navigation\";\nimport Header from \"@/components/header/Header\";\nimport { BEFORE_LOGIN_ROUTES } from \"@/constants/routes\";\n\nexport default function HeaderWrapper() {\n  const pathname = usePathname();\n\n  console.log(\"pathname\", pathname);\n  if (BEFORE_LOGIN_ROUTES.includes(pathname!)) {\n    return null;\n  }\n\n  return <Header />;\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;AAKe,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,QAAQ,GAAG,CAAC,YAAY;IACxB,IAAI,0HAAA,CAAA,sBAAmB,CAAC,QAAQ,CAAC,WAAY;QAC3C,OAAO;IACT;IAEA,qBAAO,8OAAC,sIAAA,CAAA,UAAM;;;;;AAChB", "debugId": null}}, {"offset": {"line": 2516, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}