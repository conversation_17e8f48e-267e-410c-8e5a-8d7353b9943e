(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/_38b22daa._.js", {

"[project]/src/components/svgComponents/ThreeDotsIcon.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
function ThreeDotsIcon() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            fillRule: "evenodd",
            clipRule: "evenodd",
            d: "M12.0001 19.2C13.3256 19.2 14.4001 20.2745 14.4001 21.6C14.4001 22.9255 13.3256 24 12.0001 24C10.6746 24 9.6001 22.9255 9.6001 21.6C9.6001 20.2745 10.6746 19.2 12.0001 19.2ZM12.0001 9.60005C13.3256 9.60005 14.4001 10.6746 14.4001 12C14.4001 13.3255 13.3256 14.4 12.0001 14.4C10.6746 14.4 9.6001 13.3255 9.6001 12C9.6001 10.6746 10.6746 9.60005 12.0001 9.60005ZM12.0001 0C13.3256 0 14.4001 1.07452 14.4001 2.39999C14.4001 3.72546 13.3256 4.79998 12.0001 4.79998C10.6746 4.79998 9.6001 3.72546 9.6001 2.39999C9.6001 1.07452 10.6746 0 12.0001 0Z",
            fill: "#333333"
        }, void 0, false, {
            fileName: "[project]/src/components/svgComponents/ThreeDotsIcon.tsx",
            lineNumber: 6,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/svgComponents/ThreeDotsIcon.tsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
}
_c = ThreeDotsIcon;
const __TURBOPACK__default__export__ = ThreeDotsIcon;
var _c;
__turbopack_context__.k.register(_c, "ThreeDotsIcon");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/svgComponents/ModalCloseIcon.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
const ModalCloseIcon = (props)=>{
    const { className } = props;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "40",
        height: "41",
        viewBox: "0 0 40 41",
        fill: "none",
        className: className,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                cx: "20.0003",
                cy: "20.5",
                r: "18.209",
                fill: "white"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ModalCloseIcon.tsx",
                lineNumber: 5,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M19.9997 2.16602C16.3737 2.16602 12.8292 3.24125 9.81427 5.25574C6.79937 7.27023 4.44954 10.1335 3.06193 13.4835C1.67433 16.8335 1.31126 20.5197 2.01866 24.076C2.72606 27.6323 4.47214 30.899 7.0361 33.463C9.60006 36.0269 12.8668 37.773 16.4231 38.4804C19.9794 39.1878 23.6656 38.8248 27.0156 37.4371C30.3656 36.0495 33.2288 33.6997 35.2433 30.6848C37.2578 27.6699 38.3331 24.1253 38.3331 20.4994C38.3273 15.6388 36.3939 10.979 32.957 7.54206C29.5201 4.10513 24.8603 2.17175 19.9997 2.16602ZM27.0697 25.2144C27.2289 25.3681 27.3559 25.552 27.4432 25.7553C27.5306 25.9587 27.5766 26.1774 27.5785 26.3987C27.5804 26.62 27.5382 26.8395 27.4544 27.0443C27.3706 27.2491 27.2469 27.4352 27.0904 27.5917C26.9339 27.7482 26.7478 27.8719 26.543 27.9557C26.3382 28.0395 26.1187 28.0817 25.8974 28.0798C25.6761 28.0778 25.4574 28.0319 25.2541 27.9445C25.0507 27.8572 24.8668 27.7302 24.7131 27.571L19.9997 22.856L15.2864 27.571C14.9721 27.8746 14.5511 28.0426 14.1141 28.0388C13.6771 28.035 13.259 27.8597 12.95 27.5507C12.641 27.2417 12.4657 26.8237 12.4619 26.3867C12.4581 25.9497 12.6261 25.5287 12.9297 25.2144L17.6431 20.4994L12.9297 15.7844C12.7705 15.6306 12.6436 15.4467 12.5562 15.2434C12.4689 15.04 12.4229 14.8213 12.421 14.6C12.4191 14.3787 12.4612 14.1593 12.545 13.9544C12.6288 13.7496 12.7526 13.5635 12.9091 13.407C13.0656 13.2505 13.2516 13.1268 13.4565 13.043C13.6613 12.9592 13.8808 12.917 14.1021 12.9189C14.3234 12.9209 14.5421 12.9668 14.7454 13.0542C14.9487 13.1415 15.1326 13.2685 15.2864 13.4277L19.9997 18.1427L24.7131 13.4277C24.8668 13.2685 25.0507 13.1415 25.2541 13.0542C25.4574 12.9668 25.6761 12.9209 25.8974 12.9189C26.1187 12.917 26.3382 12.9592 26.543 13.043C26.7478 13.1268 26.9339 13.2505 27.0904 13.407C27.2469 13.5635 27.3706 13.7496 27.4544 13.9544C27.5382 14.1593 27.5804 14.3787 27.5785 14.6C27.5766 14.8213 27.5306 15.04 27.4432 15.2434C27.3559 15.4467 27.2289 15.6306 27.0697 15.7844L22.3564 20.4994L27.0697 25.2144Z",
                fill: "#333333"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ModalCloseIcon.tsx",
                lineNumber: 6,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgComponents/ModalCloseIcon.tsx",
        lineNumber: 4,
        columnNumber: 5
    }, this);
};
_c = ModalCloseIcon;
const __TURBOPACK__default__export__ = ModalCloseIcon;
var _c;
__turbopack_context__.k.register(_c, "ModalCloseIcon");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/formElements/InputWrapper.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Button.tsx [app-client] (ecmascript)");
;
;
/**
 * Wrapper component for input fields
 * @param {string} className - Class name for the input field
 * @returns {JSX.Element} - Wrapper component
 */ const InputWrapper = ({ className, children })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `form-group ${className ?? ""}`,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/formElements/InputWrapper.tsx",
        lineNumber: 10,
        columnNumber: 3
    }, this);
_c = InputWrapper;
/**
 * Label component for input fields
 * @param {string} children - Label text
 * @returns {JSX.Element} - Label component
 */ InputWrapper.Label = function({ children, htmlFor, required, className, onClick, style }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
        htmlFor: htmlFor,
        className: className,
        onClick: onClick,
        style: style,
        children: [
            children,
            required ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("sup", {
                children: "*"
            }, void 0, false, {
                fileName: "[project]/src/components/formElements/InputWrapper.tsx",
                lineNumber: 37,
                columnNumber: 19
            }, this) : null
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/formElements/InputWrapper.tsx",
        lineNumber: 35,
        columnNumber: 5
    }, this);
};
/**
 * Error component for input fields to display error message
 * @param { string } message - Error message
 * @param { React.CSSProperties } style - Optional style object
 * @returns { JSX.Element } - Error component
 */ InputWrapper.Error = function({ message, style }) {
    return message ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
        className: "auth-msg error",
        style: style,
        children: message
    }, void 0, false, {
        fileName: "[project]/src/components/formElements/InputWrapper.tsx",
        lineNumber: 50,
        columnNumber: 5
    }, this) : null;
};
/**
 * Icon component for input fields
 * @param { string } src - Icon source
 * @param { function } onClick - Function to be called on click
 * @returns { JSX.Element } - Icon component
 */ InputWrapper.Icon = function({ children, // src,
onClick }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        className: "show-icon",
        type: "button",
        onClick: onClick,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/formElements/InputWrapper.tsx",
        lineNumber: 72,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = InputWrapper;
var _c;
__turbopack_context__.k.register(_c, "InputWrapper");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/formElements/Textarea.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Textarea)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
;
;
function Textarea({ control, name, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Controller"], {
        control: control,
        render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                ...props,
                value: field.value,
                onChange: field.onChange,
                "aria-label": ""
            }, void 0, false, {
                fileName: "[project]/src/components/formElements/Textarea.tsx",
                lineNumber: 13,
                columnNumber: 30
            }, void 0),
        name: name,
        defaultValue: ""
    }, void 0, false, {
        fileName: "[project]/src/components/formElements/Textarea.tsx",
        lineNumber: 11,
        columnNumber: 5
    }, this);
}
_c = Textarea;
var _c;
__turbopack_context__.k.register(_c, "Textarea");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/screenResumeServices.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "changeApplicationStatus": (()=>changeApplicationStatus),
    "getAllPendingJobApplications": (()=>getAllPendingJobApplications),
    "getPresignedUrl": (()=>getPresignedUrl),
    "processFileUpload": (()=>processFileUpload),
    "uploadManualCandidate": (()=>uploadManualCandidate),
    "uploadToS3": (()=>uploadToS3)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/endpoint.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/http.ts [app-client] (ecmascript)");
;
;
const getPresignedUrl = async (file)=>{
    const formData = new FormData();
    formData.append("file", file);
    formData.append("fileType", file.type);
    formData.append("fileName", file.name);
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["http"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].resumeScreen.GET_PRESIGNED_URL, formData, {
        headers: {
            "Content-Type": "multipart/form-data"
        }
    });
};
const uploadToS3 = async (presignedUrl, file)=>{
    return fetch(presignedUrl, {
        method: "PUT",
        body: file,
        headers: {
            "Content-Type": file.type
        }
    });
};
const processFileUpload = async (file)=>{
    try {
        // Get presigned URL
        const presignedUrlResponse = await getPresignedUrl(file);
        if (!presignedUrlResponse.data) {
            throw new Error("Failed to get presigned URL");
        }
        const responseData = presignedUrlResponse.data;
        // The response might have data nested inside another data property
        const urlData = responseData.data;
        if (!urlData.presignedUrl || !urlData.fileUrl) {
            console.error("Missing URL information in response:", urlData);
            throw new Error("Missing URL information in response");
        }
        const { presignedUrl, fileUrl, fileText } = urlData;
        // Upload file to S3
        const uploadResponse = await uploadToS3(presignedUrl, file);
        if (!uploadResponse.ok) {
            throw new Error(`Failed to upload file to S3: ${uploadResponse.status}`);
        }
        // Return the file URL and flag for backend extraction
        return {
            fileUrl,
            fileText: fileText,
            presignedUrl
        };
    } catch (error) {
        console.error("Error processing file upload:", error);
        // Include error details in the console for debugging
        if (error instanceof Error) {
            console.error("Error message:", error.message);
            console.error("Error stack:", error.stack);
        }
        throw error;
    }
};
const getAllPendingJobApplications = async (params)=>{
    try {
        // Build query parameters
        const queryParams = new URLSearchParams();
        if (params.limit) queryParams.append("limit", params.limit.toString());
        // Always include offset parameter, even when it's 0
        queryParams.append("offset", params.offset !== undefined ? params.offset.toString() : "0");
        if (params.job_id) queryParams.append("job_id", params.job_id.toString());
        if (params.status) queryParams.append("status", params.status);
        if (params.hiring_manager_id) queryParams.append("hiring_manager_id", params.hiring_manager_id.toString());
        if (params.organization_id) queryParams.append("organization_id", params.organization_id.toString());
        // Make API request
        const url = `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].resumeScreen.GET_ALL_PENDING_JOB_APPLICATIONS}?${queryParams.toString()}`;
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["http"].get(url);
    } catch (error) {
        console.error("Error fetching job applications:", error);
        throw error;
    }
};
const uploadManualCandidate = async (data, jobId)=>{
    try {
        // Process candidates with file uploads
        const processedCandidates = await Promise.all(data.candidates.map(async (candidate)=>{
            // Process resume file
            const resumeResult = candidate.resume ? await processFileUpload(candidate.resume) : {
                presignedUrl: "",
                fileUrl: "",
                fileText: ""
            };
            // Process assessment file
            const assessmentResult = candidate.assessment ? await processFileUpload(candidate.assessment) : {
                presignedUrl: "",
                fileUrl: "",
                fileText: ""
            };
            return {
                name: candidate.name,
                email: candidate.email,
                gender: candidate.gender,
                additional_details: candidate.additionalInfo || "",
                resume_file: resumeResult.presignedUrl,
                resume_text: resumeResult.fileText,
                assessment_file: assessmentResult.presignedUrl,
                assessment_text: assessmentResult.fileText
            };
        }));
        // Create payload for API
        const payload = {
            job_id: jobId,
            candidates: processedCandidates
        };
        // Send to backend API
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["http"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].resumeScreen.MANUAL_CANDIDATE_UPLOAD, payload);
    } catch (error) {
        console.error("Error in uploadManualCandidate:", error);
        throw error;
    }
};
const changeApplicationStatus = async (data)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["http"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].resumeScreen.CHANGE_APPLICATION_STATUS, data);
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/constants/jobRequirementConstant.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "APPLICATION_STATUS": (()=>APPLICATION_STATUS),
    "CATEGORY_OPTION": (()=>CATEGORY_OPTION),
    "COMPLIANCE_LINK": (()=>COMPLIANCE_LINK),
    "COMPLIANCE_OPTIONS": (()=>COMPLIANCE_OPTIONS),
    "CURRENCY_SYMBOL": (()=>CURRENCY_SYMBOL),
    "CURSOR_POINT": (()=>CURSOR_POINT),
    "DEPARTMENT_OPTION": (()=>DEPARTMENT_OPTION),
    "EXPERIENCE_LEVEL_OPTIONS": (()=>EXPERIENCE_LEVEL_OPTIONS),
    "FILE_NAME": (()=>FILE_NAME),
    "FILE_SIZE_LIMIT": (()=>FILE_SIZE_LIMIT),
    "FILE_TYPE": (()=>FILE_TYPE),
    "HIRING_TYPE": (()=>HIRING_TYPE),
    "HIRING_TYPE_KEY": (()=>HIRING_TYPE_KEY),
    "JOB_GENERATION_UPLOAD_MESSAGES": (()=>JOB_GENERATION_UPLOAD_MESSAGES),
    "LOCATION_TYPE_OPTIONS": (()=>LOCATION_TYPE_OPTIONS),
    "MAX_FILE_SIZE": (()=>MAX_FILE_SIZE),
    "SALARY_CYCLE_OPTIONS": (()=>SALARY_CYCLE_OPTIONS),
    "SALARY_REMOVE_SYMBOL_REGEX": (()=>SALARY_REMOVE_SYMBOL_REGEX),
    "SKILL_CATEGORY": (()=>SKILL_CATEGORY),
    "SKILL_TYPE": (()=>SKILL_TYPE),
    "SUN_EDITOR_BUTTON_LIST": (()=>SUN_EDITOR_BUTTON_LIST),
    "TONE_STYLE_OPTIONS": (()=>TONE_STYLE_OPTIONS)
});
const CATEGORY_OPTION = [
    {
        label: "Full time",
        value: "full_time"
    },
    {
        label: "Part time",
        value: "part_time"
    },
    {
        label: "Contract",
        value: "contract"
    },
    {
        label: "Internship",
        value: "internship"
    },
    {
        label: "Freelance",
        value: "freelance"
    }
];
const SALARY_CYCLE_OPTIONS = [
    {
        label: "Per Hour",
        value: "per hour"
    },
    {
        label: "Per Month",
        value: "per month"
    },
    {
        label: "Per Annum",
        value: "per annum"
    }
];
const LOCATION_TYPE_OPTIONS = [
    {
        label: "Remote",
        value: "remote"
    },
    {
        label: "Hybrid",
        value: "hybrid"
    },
    {
        label: "On-site",
        value: "onsite"
    }
];
const TONE_STYLE_OPTIONS = [
    {
        label: "Professional & Formal",
        value: "Professional_Formal"
    },
    {
        label: "Conversational & Approachable",
        value: "Conversational_Approachable"
    },
    {
        label: "Bold & Energetic",
        value: "Bold_Energetic"
    },
    {
        label: "Inspirational & Mission-Driven",
        value: "Inspirational_Mission-Driven"
    },
    {
        label: "Technical & Precise",
        value: "Technical_Precise"
    },
    {
        label: "Creative & Fun",
        value: "Creative_Fun"
    },
    {
        label: "Inclusive & Human-Centered",
        value: "Inclusive_Human-Centered"
    },
    {
        label: "Minimalist & Straightforward",
        value: "Minimalist_Straightforward"
    }
];
const COMPLIANCE_OPTIONS = [
    {
        label: "Equal Employment Opportunity (EEO) Statement",
        value: "Equal Employment Opportunity (EEO) Statement"
    },
    {
        label: "Affirmative Action Statement (For Federal Contractors or Affirmative Action Employers)",
        value: "Affirmative Action Statement (For Federal Contractors or Affirmative Action Employers)"
    },
    {
        label: "Disability Accommodation Statement",
        value: "Disability Accommodation Statement"
    },
    {
        label: "Veterans Preference Statement (For Government Agencies and Federal Contractors)",
        value: "Veterans Preference Statement (For Government Agencies and Federal Contractors)"
    },
    {
        label: "Diversity & Inclusion Commitment",
        value: "Diversity & Inclusion Commitment"
    },
    {
        label: "Pay Transparency Non-Discrimination Statement (For Federal Contractors)",
        value: "Pay Transparency Non-Discrimination Statement (For Federal Contractors)"
    },
    {
        label: "Background Check and Drug-Free Workplace Policy (If Applicable)",
        value: "Background Check and Drug-Free Workplace Policy (If Applicable)"
    },
    {
        label: "Work Authorization & Immigration Statement",
        value: "Work Authorization & Immigration Statement"
    }
];
const EXPERIENCE_LEVEL_OPTIONS = [
    {
        label: "General",
        value: "General"
    },
    {
        label: "No experience necessary",
        value: "No experience necessary"
    },
    {
        label: "Entry-Level Position",
        value: "Entry-Level Position"
    },
    {
        label: "Mid-Level Professional",
        value: "Mid-Level Professional"
    },
    {
        label: "Senior/Experienced Professional",
        value: "Senior/Experienced Professional"
    },
    {
        label: "Managerial/Executive Level",
        value: "Managerial/Executive Level"
    },
    {
        label: "Specialized Expert",
        value: "Specialized Expert"
    }
];
const DEPARTMENT_OPTION = [
    {
        label: "IT",
        value: "IT"
    },
    {
        label: "HR",
        value: "HR"
    },
    {
        label: "Marketing",
        value: "Marketing"
    },
    {
        label: "Finance",
        value: "Finance"
    },
    {
        label: "Sales",
        value: "Sales"
    }
];
const FILE_SIZE_LIMIT = 5 * 1024 * 1024; // 5MB
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const FILE_TYPE = "application/pdf";
const FILE_NAME = ".pdf";
const SALARY_REMOVE_SYMBOL_REGEX = /[\$\s]/g;
const CURRENCY_SYMBOL = "$";
const SUN_EDITOR_BUTTON_LIST = [
    [
        "font",
        "fontSize",
        "formatBlock"
    ],
    [
        "bold",
        "underline",
        "italic"
    ],
    [
        "fontColor",
        "hiliteColor"
    ],
    [
        "align",
        "list",
        "lineHeight"
    ]
];
const HIRING_TYPE = {
    INTERNAL: "internal",
    EXTERNAL: "external"
};
const SKILL_CATEGORY = {
    Personal_Health: "Personal Health",
    Social_Interaction: "Social Interaction",
    Mastery_Of_Emotions: "Mastery of Emotions",
    Mentality: "Mentality",
    Cognitive_Abilities: "Cognitive Abilities"
};
const APPLICATION_STATUS = {
    PENDING: "Pending",
    APPROVED: "Approved",
    REJECTED: "Rejected",
    HIRED: "Hired",
    ON_HOLD: "On-Hold",
    FINAL_REJECT: "Final-Reject"
};
const SKILL_TYPE = {
    ROLE: "role",
    CULTURE: "culture"
};
const HIRING_TYPE_KEY = "hiringType";
const CURSOR_POINT = {
    cursor: "pointer"
};
const COMPLIANCE_LINK = "https://s9-interview-assets.s3.us-east-1.amazonaws.com/A+comprehensive+compliance+section.pdf";
const JOB_GENERATION_UPLOAD_MESSAGES = [
    "Analyzing your job description...",
    "Extracting key requirements...",
    "Processing document content...",
    "Identifying skills and qualifications...",
    "Parsing job details...",
    "Almost ready..."
];
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/commonModals/CandidateApproveRejectModal.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$ModalCloseIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/ModalCloseIcon.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/InputWrapper.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Textarea$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Textarea.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$screenResumeServices$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/screenResumeServices.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/jobRequirementConstant.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/helper.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
const CandidateApproveRejectModal = ({ onClickCancel, candidate, onSuccess })=>{
    _s();
    const authData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"])({
        "CandidateApproveRejectModal.useSelector[authData]": (state)=>state.auth.authData
    }["CandidateApproveRejectModal.useSelector[authData]"]);
    const [isSubmitting, setIsSubmitting] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [success, setSuccess] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [selectedStatus, setSelectedStatus] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].ON_HOLD);
    const { control, handleSubmit, formState: { errors } } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])({
        defaultValues: {
            reason: ""
        },
        mode: "onSubmit",
        criteriaMode: "firstError",
        shouldFocusError: true,
        reValidateMode: "onChange",
        resolver: {
            "CandidateApproveRejectModal.useForm": (values)=>{
                const errors = {};
                // Required validation for reason field
                if (!values.reason || values.reason.trim() === "") {
                    errors.reason = {
                        type: "required",
                        message: "Please provide a reason"
                    };
                } else if (values.reason.trim().length < 5) {
                    errors.reason = {
                        type: "minLength",
                        message: "Reason should be at least 5 characters long"
                    };
                } else if (values.reason.trim().length > 50) {
                    errors.reason = {
                        type: "maxLength",
                        message: "Reason should not exceed 50 characters"
                    };
                }
                return {
                    values,
                    errors
                };
            }
        }["CandidateApproveRejectModal.useForm"]
    });
    const onSubmit = async (formData)=>{
        if (!candidate || !authData) return;
        try {
            setIsSubmitting(true);
            setError("");
            const data = {
                job_id: candidate.job_id,
                candidate_id: candidate.candidateId,
                hiring_manager_id: authData.id,
                status: selectedStatus,
                hiring_manager_reason: formData.reason
            };
            const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$screenResumeServices$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["changeApplicationStatus"])(data);
            if (response.data && response.data.success) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageSuccess"])("Candidate status has been updated successfully!");
                setSuccess(true);
                // Call the onSuccess callback if provided
                if (onSuccess) {
                    setTimeout(()=>{
                        onClickCancel();
                        onSuccess(candidate, selectedStatus);
                    }, 1500);
                }
            } else {
                setError(response.data?.message || "Failed to update candidate status");
            }
        } catch (err) {
            console.error("Error updating candidate status:", err);
            setError("An unexpected error occurred. Please try again.");
        } finally{
            setIsSubmitting(false);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "modal theme-modal show-modal",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "modal-dialog modal-dialog-centered",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "modal-content",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "modal-header justify-content-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                children: "Review Candidate"
                            }, void 0, false, {
                                fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
                                lineNumber: 112,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                children: "Please review the candidate's profile and make a decision."
                            }, void 0, false, {
                                fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
                                lineNumber: 113,
                                columnNumber: 13
                            }, this),
                            !isSubmitting && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                className: "modal-close-btn",
                                onClick: onClickCancel,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$ModalCloseIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                    fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
                                    lineNumber: 116,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
                                lineNumber: 115,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
                        lineNumber: 111,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "modal-body",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "qualification-card",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "qualification-card-top",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "name",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                        children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toTitleCase"])(candidate?.candidateName || "Candidate")
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
                                                        lineNumber: 125,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        children: [
                                                            candidate?.aiDecision || "Pending",
                                                            " by S9 Interviews"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
                                                        lineNumber: 126,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
                                                lineNumber: 124,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "top-right",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "on-hold-status",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        children: candidate?.applicationStatus
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
                                                        lineNumber: 130,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
                                                    lineNumber: 129,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
                                                lineNumber: 128,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
                                        lineNumber: 123,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "qualification-card-mid",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("b", {
                                                    children: "Reasons why they are a good match:"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
                                                    lineNumber: 136,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
                                                lineNumber: 135,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                children: candidate?.aiReason || "No reason provided by AI evaluation."
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
                                                lineNumber: 138,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
                                        lineNumber: 134,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
                                lineNumber: 122,
                                columnNumber: 13
                            }, this),
                            !success && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                                    onSubmit: handleSubmit(onSubmit),
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Label, {
                                                    htmlFor: "reason",
                                                    required: true,
                                                    children: "Reason"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
                                                    lineNumber: 148,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Textarea$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    rows: 6,
                                                    name: "reason",
                                                    control: control,
                                                    placeholder: "Enter your reason here",
                                                    className: "form-control"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
                                                    lineNumber: 151,
                                                    columnNumber: 21
                                                }, this),
                                                errors.reason && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-danger mt-1",
                                                    children: errors.reason.message
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
                                                    lineNumber: 152,
                                                    columnNumber: 39
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
                                            lineNumber: 147,
                                            columnNumber: 19
                                        }, this),
                                        error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "error-message alert alert-danger my-3",
                                            children: error
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
                                            lineNumber: 155,
                                            columnNumber: 29
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "action-btn gap-3 mt-4",
                                            children: isSubmitting ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-center w-100",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "spinner-border text-primary",
                                                        role: "status",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "visually-hidden",
                                                            children: "Submitting..."
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
                                                            lineNumber: 161,
                                                            columnNumber: 27
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
                                                        lineNumber: 160,
                                                        columnNumber: 25
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "mt-2",
                                                        children: "Submitting..."
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
                                                        lineNumber: 163,
                                                        columnNumber: 25
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
                                                lineNumber: 159,
                                                columnNumber: 23
                                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        type: "button",
                                                        className: "primary-btn rounded-md w-100",
                                                        onClick: ()=>{
                                                            setSelectedStatus(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].APPROVED);
                                                            handleSubmit(onSubmit)();
                                                        },
                                                        children: "Approve Candidate"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
                                                        lineNumber: 167,
                                                        columnNumber: 25
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        type: "button",
                                                        className: "danger-btn rounded-md w-100",
                                                        onClick: ()=>{
                                                            setSelectedStatus(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].REJECTED);
                                                            handleSubmit(onSubmit)();
                                                        },
                                                        children: "Reject Candidate"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
                                                        lineNumber: 177,
                                                        columnNumber: 25
                                                    }, this)
                                                ]
                                            }, void 0, true)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
                                            lineNumber: 157,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
                                    lineNumber: 146,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
                        lineNumber: 120,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
                lineNumber: 110,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
            lineNumber: 109,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/commonModals/CandidateApproveRejectModal.tsx",
        lineNumber: 108,
        columnNumber: 5
    }, this);
};
_s(CandidateApproveRejectModal, "ZoM2ik//5TpBxCRvDXhiBXAQQfY=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"]
    ];
});
_c = CandidateApproveRejectModal;
const __TURBOPACK__default__export__ = CandidateApproveRejectModal;
var _c;
__turbopack_context__.k.register(_c, "CandidateApproveRejectModal");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/svgComponents/BackArrowIcon.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
function BackArrowIcon({ onClick }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        className: "cursor-pointer me-3",
        width: "26",
        height: "26",
        viewBox: "0 0 32 32",
        fill: "none",
        onClick: onClick,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M28.6843 14.6661H5.23629L12.2936 7.60879C12.421 7.48579 12.5225 7.33867 12.5924 7.176C12.6623 7.01332 12.6991 6.83836 12.7006 6.66133C12.7022 6.48429 12.6684 6.30871 12.6014 6.14485C12.5343 5.98099 12.4353 5.83212 12.3101 5.70693C12.185 5.58174 12.0361 5.48274 11.8722 5.41569C11.7084 5.34865 11.5328 5.31492 11.3558 5.31646C11.1787 5.318 11.0038 5.35478 10.8411 5.42466C10.6784 5.49453 10.5313 5.59611 10.4083 5.72346L1.07495 15.0568C0.824991 15.3068 0.68457 15.6459 0.68457 15.9995C0.68457 16.353 0.824991 16.6921 1.07495 16.9421L10.4083 26.2755C10.6598 26.5183 10.9966 26.6527 11.3462 26.6497C11.6957 26.6467 12.0302 26.5064 12.2774 26.2592C12.5246 26.012 12.6648 25.6776 12.6679 25.328C12.6709 24.9784 12.5365 24.6416 12.2936 24.3901L5.23629 17.3328H28.6843C29.0379 17.3328 29.377 17.1923 29.6271 16.9423C29.8771 16.6922 30.0176 16.3531 30.0176 15.9995C30.0176 15.6458 29.8771 15.3067 29.6271 15.0566C29.377 14.8066 29.0379 14.6661 28.6843 14.6661Z",
            fill: "#333333"
        }, void 0, false, {
            fileName: "[project]/src/components/svgComponents/BackArrowIcon.tsx",
            lineNumber: 10,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/svgComponents/BackArrowIcon.tsx",
        lineNumber: 9,
        columnNumber: 5
    }, this);
}
_c = BackArrowIcon;
const __TURBOPACK__default__export__ = BackArrowIcon;
var _c;
__turbopack_context__.k.register(_c, "BackArrowIcon");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/commonModals/ArchiveCandidateModal.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$ModalCloseIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/ModalCloseIcon.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/InputWrapper.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Textarea$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Textarea.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
const ArchiveCandidateModal = ({ onClickCancel, onSuccess })=>{
    _s();
    const authData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"])({
        "ArchiveCandidateModal.useSelector[authData]": (state)=>state.auth.authData
    }["ArchiveCandidateModal.useSelector[authData]"]);
    const [isSubmitting, setIsSubmitting] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const { control, handleSubmit, formState: { errors } } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])({
        defaultValues: {
            reason: ""
        },
        mode: "onSubmit",
        criteriaMode: "firstError",
        shouldFocusError: true,
        reValidateMode: "onChange",
        resolver: {
            "ArchiveCandidateModal.useForm": (values)=>{
                const errors = {};
                if (!values.reason || values.reason.trim() === "") {
                    errors.reason = {
                        type: "required",
                        message: "Please provide a reason"
                    };
                }
                if (values.reason.trim().length < 5) {
                    errors.reason = {
                        type: "minLength",
                        message: "Reason should be at least 5 characters long"
                    };
                } else if (values.reason.trim().length > 50) {
                    errors.reason = {
                        type: "maxLength",
                        message: "Reason should not exceed 50 characters"
                    };
                }
                return {
                    values,
                    errors
                };
            }
        }["ArchiveCandidateModal.useForm"]
    });
    const onSubmit = async (data)=>{
        if (!authData) return;
        try {
            setIsSubmitting(true);
            setError("");
            if (onSuccess) await onSuccess(data.reason);
        } catch (err) {
            console.error("Archive candidate error:", err);
            setError("An unexpected error occurred. Please try again.");
        } finally{
            setIsSubmitting(false);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "modal theme-modal show-modal",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "modal-dialog modal-dialog-centered",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "modal-content",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "modal-header justify-content-between pb-0 ",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                className: "m-0",
                                children: "Archive Candidate"
                            }, void 0, false, {
                                fileName: "[project]/src/components/commonModals/ArchiveCandidateModal.tsx",
                                lineNumber: 66,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                className: "modal-close-btn",
                                onClick: onClickCancel,
                                disabled: isSubmitting,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$ModalCloseIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                    fileName: "[project]/src/components/commonModals/ArchiveCandidateModal.tsx",
                                    lineNumber: 68,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/commonModals/ArchiveCandidateModal.tsx",
                                lineNumber: 67,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/commonModals/ArchiveCandidateModal.tsx",
                        lineNumber: 65,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "modal-body",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                            onSubmit: handleSubmit(onSubmit),
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Label, {
                                            htmlFor: "reason",
                                            required: true,
                                            children: "Please provide a reason"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/commonModals/ArchiveCandidateModal.tsx",
                                            lineNumber: 74,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Textarea$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            rows: 5,
                                            name: "reason",
                                            control: control,
                                            placeholder: "Enter reason for archiving candidate",
                                            className: "form-control"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/commonModals/ArchiveCandidateModal.tsx",
                                            lineNumber: 77,
                                            columnNumber: 17
                                        }, this),
                                        errors.reason && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-danger mt-1",
                                            children: errors.reason.message
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/commonModals/ArchiveCandidateModal.tsx",
                                            lineNumber: 78,
                                            columnNumber: 35
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/commonModals/ArchiveCandidateModal.tsx",
                                    lineNumber: 73,
                                    columnNumber: 15
                                }, this),
                                error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "alert alert-danger my-3",
                                    children: error
                                }, void 0, false, {
                                    fileName: "[project]/src/components/commonModals/ArchiveCandidateModal.tsx",
                                    lineNumber: 81,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "d-flex justify-content-end gap-2 mt-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            type: "button",
                                            className: "secondary-btn rounded-md w-100",
                                            onClick: onClickCancel,
                                            disabled: isSubmitting,
                                            children: "Cancel"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/commonModals/ArchiveCandidateModal.tsx",
                                            lineNumber: 84,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            type: "submit",
                                            className: "primary-btn rounded-md w-100",
                                            disabled: isSubmitting,
                                            children: isSubmitting ? "Archiving..." : "Archive"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/commonModals/ArchiveCandidateModal.tsx",
                                            lineNumber: 87,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/commonModals/ArchiveCandidateModal.tsx",
                                    lineNumber: 83,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/commonModals/ArchiveCandidateModal.tsx",
                            lineNumber: 72,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/commonModals/ArchiveCandidateModal.tsx",
                        lineNumber: 71,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/commonModals/ArchiveCandidateModal.tsx",
                lineNumber: 64,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/commonModals/ArchiveCandidateModal.tsx",
            lineNumber: 63,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/commonModals/ArchiveCandidateModal.tsx",
        lineNumber: 62,
        columnNumber: 5
    }, this);
};
_s(ArchiveCandidateModal, "R6ir1ntHvcLxbWOlJOioPI63MWU=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"]
    ];
});
_c = ArchiveCandidateModal;
const __TURBOPACK__default__export__ = ArchiveCandidateModal;
var _c;
__turbopack_context__.k.register(_c, "ArchiveCandidateModal");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/CandidatesServices/candidatesApplicationServices.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "addApplicantAdditionalInfo": (()=>addApplicantAdditionalInfo),
    "fetchCandidateProfile": (()=>fetchCandidateProfile),
    "fetchCandidatesApplications": (()=>fetchCandidatesApplications),
    "fetchTopCandidatesApplications": (()=>fetchTopCandidatesApplications),
    "promoteDemoteCandidate": (()=>promoteDemoteCandidate),
    "updateJobApplicationStatus": (()=>updateJobApplicationStatus)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/endpoint.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/http.ts [app-client] (ecmascript)");
;
;
const fetchCandidatesApplications = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].candidatesApplication.GET_CANDIDATES_WITH_APPLICATIONS, {
        ...data
    });
};
const fetchTopCandidatesApplications = (jobId)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].candidatesApplication.GET_TOP_CANDIDATES_WITH_APPLICATIONS, {
        jobId
    });
};
const promoteDemoteCandidate = async (payload)=>{
    return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["put"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].candidatesApplication.PROMOTE_DEMOTE_CANDIDATE, payload);
};
const addApplicantAdditionalInfo = async (payload)=>{
    return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].candidatesApplication.ADDITIONAL_INFO, payload);
};
const fetchCandidateProfile = (candidateId)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].candidatesApplication.GET_CANDIDATE_DETAILS, {
        candidateId
    });
};
const updateJobApplicationStatus = async (jobApplicationId, status)=>{
    return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["put"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].candidatesApplication.UPDATE_JOB_APPLICATION_STATUS.replace(":jobApplicationId", jobApplicationId.toString()), {
        status
    });
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/CandidatesServices/candidatesApplicationStatusUpdateService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// candidatesApplicationServices.ts
__turbopack_context__.s({
    "archiveActiveApplication": (()=>archiveActiveApplication)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/endpoint.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/http.ts [app-client] (ecmascript)");
;
;
const archiveActiveApplication = (applicationId, isActive, reason)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["put"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].candidatesApplication.ARCHIVE_ACTIVE_APPLICATION.replace(":applicationId", applicationId.toString()), {
        isActive,
        reason
    });
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/styles/commonPage.module.scss.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "active": "commonPage-module-scss-module__em0r7a__active",
  "add_another_candidate_link": "commonPage-module-scss-module__em0r7a__add_another_candidate_link",
  "approved_status_indicator": "commonPage-module-scss-module__em0r7a__approved_status_indicator",
  "border_none": "commonPage-module-scss-module__em0r7a__border_none",
  "candidate_card": "commonPage-module-scss-module__em0r7a__candidate_card",
  "candidate_card_header": "commonPage-module-scss-module__em0r7a__candidate_card_header",
  "candidate_qualification_page": "commonPage-module-scss-module__em0r7a__candidate_qualification_page",
  "candidates_list_page": "commonPage-module-scss-module__em0r7a__candidates_list_page",
  "candidates_list_section": "commonPage-module-scss-module__em0r7a__candidates_list_section",
  "career-skill-card": "commonPage-module-scss-module__em0r7a__career-skill-card",
  "dashboard__stat": "commonPage-module-scss-module__em0r7a__dashboard__stat",
  "dashboard__stat_design": "commonPage-module-scss-module__em0r7a__dashboard__stat_design",
  "dashboard__stat_image": "commonPage-module-scss-module__em0r7a__dashboard__stat_image",
  "dashboard__stat_label": "commonPage-module-scss-module__em0r7a__dashboard__stat_label",
  "dashboard__stat_value": "commonPage-module-scss-module__em0r7a__dashboard__stat_value",
  "dashboard__stats": "commonPage-module-scss-module__em0r7a__dashboard__stats",
  "dashboard_inner_head": "commonPage-module-scss-module__em0r7a__dashboard_inner_head",
  "dashboard_page": "commonPage-module-scss-module__em0r7a__dashboard_page",
  "header_tab": "commonPage-module-scss-module__em0r7a__header_tab",
  "inner_heading": "commonPage-module-scss-module__em0r7a__inner_heading",
  "inner_page": "commonPage-module-scss-module__em0r7a__inner_page",
  "input_type_file": "commonPage-module-scss-module__em0r7a__input_type_file",
  "interview_form_icon": "commonPage-module-scss-module__em0r7a__interview_form_icon",
  "job_info": "commonPage-module-scss-module__em0r7a__job_info",
  "job_page": "commonPage-module-scss-module__em0r7a__job_page",
  "manual_upload_resume": "commonPage-module-scss-module__em0r7a__manual_upload_resume",
  "operation_admins_img": "commonPage-module-scss-module__em0r7a__operation_admins_img",
  "resume_page": "commonPage-module-scss-module__em0r7a__resume_page",
  "search_box": "commonPage-module-scss-module__em0r7a__search_box",
  "section_heading": "commonPage-module-scss-module__em0r7a__section_heading",
  "section_name": "commonPage-module-scss-module__em0r7a__section_name",
  "selected": "commonPage-module-scss-module__em0r7a__selected",
  "selecting": "commonPage-module-scss-module__em0r7a__selecting",
  "selection": "commonPage-module-scss-module__em0r7a__selection",
  "skills_info_box": "commonPage-module-scss-module__em0r7a__skills_info_box",
  "skills_tab": "commonPage-module-scss-module__em0r7a__skills_tab",
  "text_xs": "commonPage-module-scss-module__em0r7a__text_xs",
  "upload_resume_page": "commonPage-module-scss-module__em0r7a__upload_resume_page",
});
}}),
"[project]/src/constants/screenResumeConstant.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "APPLICATION_UPDATE_STATUS": (()=>APPLICATION_UPDATE_STATUS),
    "GENDER_OPTIONS": (()=>GENDER_OPTIONS),
    "InterviewTabType": (()=>InterviewTabType)
});
const GENDER_OPTIONS = [
    {
        value: "Male",
        label: "Male"
    },
    {
        value: "Female",
        label: "Female"
    }
];
var InterviewTabType = /*#__PURE__*/ function(InterviewTabType) {
    InterviewTabType["UPCOMING"] = "UpcomingInterviews";
    InterviewTabType["PAST"] = "PastInterviews";
    return InterviewTabType;
}({});
const APPLICATION_UPDATE_STATUS = {
    PROMOTED: "Promoted",
    DEMOTED: "Demoted"
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/views/skeletons/TableSkeleton.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-loading-skeleton/dist/index.js [app-client] (ecmascript)");
;
;
;
const TableSkeleton = ({ rows = 3, cols = 3, colWidths = "120,80,100" })=>{
    const columnWidths = colWidths.split(",").map((w)=>w.trim());
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
        children: [
            ...Array(rows)
        ].map((_, rowIndex)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                children: [
                    ...Array(cols)
                ].map((_, colIndex)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                        className: "text-center",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            width: columnWidths[colIndex] || 80,
                            height: 20,
                            circle: false
                        }, void 0, false, {
                            fileName: "[project]/src/components/views/skeletons/TableSkeleton.tsx",
                            lineNumber: 14,
                            columnNumber: 15
                        }, this)
                    }, `loader-col-${colIndex}`, false, {
                        fileName: "[project]/src/components/views/skeletons/TableSkeleton.tsx",
                        lineNumber: 13,
                        columnNumber: 13
                    }, this))
            }, `loader-row-${rowIndex}`, false, {
                fileName: "[project]/src/components/views/skeletons/TableSkeleton.tsx",
                lineNumber: 11,
                columnNumber: 9
            }, this))
    }, void 0, false, {
        fileName: "[project]/src/components/views/skeletons/TableSkeleton.tsx",
        lineNumber: 9,
        columnNumber: 5
    }, this);
};
_c = TableSkeleton;
const __TURBOPACK__default__export__ = TableSkeleton;
var _c;
__turbopack_context__.k.register(_c, "TableSkeleton");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/assets/fullPageLoader.gif (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/fullPageLoader.c86edb97.gif");}}),
"[project]/public/assets/fullPageLoader.gif.mjs { IMAGE => \"[project]/public/assets/fullPageLoader.gif (static in ecmascript)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$fullPageLoader$2e$gif__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/public/assets/fullPageLoader.gif (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$fullPageLoader$2e$gif__$28$static__in__ecmascript$29$__["default"],
    width: 1200,
    height: 857,
    blurDataURL: null,
    blurWidth: 0,
    blurHeight: 0
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/commonComponent/FullPageLoader.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$fullPageLoader$2e$gif$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$assets$2f$fullPageLoader$2e$gif__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/assets/fullPageLoader.gif.mjs { IMAGE => "[project]/public/assets/fullPageLoader.gif (static in ecmascript)" } [app-client] (structured image object, ecmascript)');
;
;
;
const FullPageLoader = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex items-center justify-center h-screen",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$fullPageLoader$2e$gif$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$assets$2f$fullPageLoader$2e$gif__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
            alt: "Loading..."
        }, void 0, false, {
            fileName: "[project]/src/components/commonComponent/FullPageLoader.tsx",
            lineNumber: 7,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/commonComponent/FullPageLoader.tsx",
        lineNumber: 6,
        columnNumber: 5
    }, this);
};
_c = FullPageLoader;
const __TURBOPACK__default__export__ = FullPageLoader;
var _c;
__turbopack_context__.k.register(_c, "FullPageLoader");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/views/resume/CandidatesList.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* eslint-disable react-hooks/exhaustive-deps */ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$dayjs$2e$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/dayjs/dayjs.min.js [app-client] (ecmascript)");
// import { useForm } from "react-hook-form";
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-client] (ecmascript)");
// import InfiniteScroll from "react-infinite-scroll-component";
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Button.tsx [app-client] (ecmascript)");
// import InputWrapper from "@/components/formElements/InputWrapper";
// import Textbox from "@/components/formElements/Textbox";
// import SearchIcon from "@/components/svgComponents/SearchIcon";
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$ThreeDotsIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/ThreeDotsIcon.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$commonModals$2f$CandidateApproveRejectModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/commonModals/CandidateApproveRejectModal.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$BackArrowIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/BackArrowIcon.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$commonModals$2f$ArchiveCandidateModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/commonModals/ArchiveCandidateModal.tsx [app-client] (ecmascript)"); //
// Services
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$CandidatesServices$2f$candidatesApplicationServices$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/CandidatesServices/candidatesApplicationServices.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$CandidatesServices$2f$candidatesApplicationStatusUpdateService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/CandidatesServices/candidatesApplicationStatusUpdateService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/commonConstants.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/jobRequirementConstant.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$commonPage$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/styles/commonPage.module.scss.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/use-intl/dist/esm/development/react.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$infinite$2d$scroll$2d$component$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-infinite-scroll-component/dist/index.es.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$screenResumeConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/screenResumeConstant.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/helper.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$skeletons$2f$TableSkeleton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/views/skeletons/TableSkeleton.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/routes.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2f$debounce$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lodash/debounce.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$commonComponent$2f$FullPageLoader$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/commonComponent/FullPageLoader.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
// import InputWrapper from "@/components/formElements/InputWrapper";
// import Textbox from "@/components/formElements/Textbox";
// import SearchIcon from "@/components/svgComponents/SearchIcon";
// import { useForm } from "react-hook-form";
const CandidatesList = ({ params, searchParams })=>{
    _s();
    // const { control } = useForm();
    const userData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"])({
        "CandidatesList.useSelector[userData]": (state)=>state.auth.authData
    }["CandidatesList.useSelector[userData]"]);
    const userPermissions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"])({
        "CandidatesList.useSelector[userPermissions]": (state)=>state.auth.permissions || []
    }["CandidatesList.useSelector[userPermissions]"]);
    const hasArchiveRestoreCandidatesPermission = userPermissions.includes(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PERMISSION"].ARCHIVE_RESTORE_CANDIDATES);
    const hasManualResumeScreeningPermission = userPermissions.includes(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PERMISSION"].MANUAL_RESUME_SCREENING);
    const hasEditScheduledInterviewsPermission = userPermissions.includes(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PERMISSION"].EDIT_SCHEDULED_INTERVIEWS);
    const hasAddAdditionalCandidateInfoPermission = userPermissions.includes(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PERMISSION"].ADD_ADDITIONAL_CANDIDATE_INFO);
    const hasManageTopCandidatesPermission = userPermissions.includes(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PERMISSION"].MANAGE_TOP_CANDIDATES);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const paramsPromise = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].use(params);
    const searchParamsPromise = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].use(searchParams);
    // State for candidates data
    const [candidates, setCandidates] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [loader, setLoader] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [hasMore, setHasMore] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [offset, setOffset] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [activeDropdown, setActiveDropdown] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [selectedCandidate, setSelectedCandidate] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [showReviewModal, setShowReviewModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showArchiveModal, setShowArchiveModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false); //
    const [searchStr] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [topCandidates, setTopCandidates] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [disable, setDisable] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTranslations"])();
    const dropdownRefs = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useRef({});
    const [openDropdownId, setOpenDropdownId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loadingFullScreen, setFullScreenLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CandidatesList.useEffect": ()=>{
            if (!Number(paramsPromise.jobId) || !searchParamsPromise.title) {
                router.push(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].DASHBOARD);
            }
        }
    }["CandidatesList.useEffect"], [
        paramsPromise.jobId
    ]);
    // const observer = useRef<IntersectionObserver | null>(null);
    const fetchTopCandidates = async ()=>{
        if (!userData?.orgId || !paramsPromise?.jobId) return;
        setLoading(true);
        try {
            const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$CandidatesServices$2f$candidatesApplicationServices$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchTopCandidatesApplications"])(Number(paramsPromise.jobId));
            if (response?.data?.success) {
                setTopCandidates(response.data.data);
            } else {
                setTopCandidates([]);
            }
        } catch  {
            setTopCandidates([]);
        } finally{
            setLoading(false);
        }
    };
    const handlePromoteDemoteCandidate = async (candidate, action)=>{
        setDisable(true);
        setFullScreenLoading(true);
        try {
            const payload = {
                candidateId: candidate.candidateId,
                applicationId: candidate.applicationId,
                action
            };
            const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$CandidatesServices$2f$candidatesApplicationServices$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["promoteDemoteCandidate"])(payload);
            if (response?.data?.success) {
                if (action === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$screenResumeConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["APPLICATION_UPDATE_STATUS"].PROMOTED) {
                    // Candidate is currently in the 'other candidates' list
                    const fromOther = candidate;
                    // Remove from other candidates
                    setCandidates((prev)=>prev.filter((c)=>c.applicationId !== fromOther.applicationId));
                    // Add to top candidates
                    const promoted = {
                        candidateName: fromOther.candidateName,
                        applicationCreatedTs: fromOther.applicationCreatedTs,
                        atsScore: fromOther.atsScore,
                        applicationId: fromOther.applicationId,
                        applicationRankStatus: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$screenResumeConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["APPLICATION_UPDATE_STATUS"].PROMOTED,
                        candidateId: fromOther.candidateId,
                        aiReason: fromOther.aiReason,
                        aiDecision: fromOther.aiDecision,
                        applicationStatus: fromOther.applicationStatus,
                        hiringManagerReason: fromOther.hiringManagerReason,
                        applicationUpdatedTs: new Date().toISOString(),
                        applicationSource: fromOther.applicationSource || "",
                        job_id: fromOther.job_id || 0,
                        isTopApplication: fromOther.isTopApplication || false
                    };
                    setTopCandidates((prev)=>[
                            ...prev,
                            promoted
                        ]);
                } else if (action === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$screenResumeConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["APPLICATION_UPDATE_STATUS"].DEMOTED) {
                    // Candidate is currently in the 'top candidates' list
                    const fromTop = candidate;
                    // Remove from top candidates
                    setTopCandidates((prev)=>prev.filter((c)=>c.applicationId !== fromTop.applicationId));
                    // Add to other candidates
                    const demoted = {
                        candidateId: fromTop.candidateId,
                        candidateName: fromTop.candidateName,
                        applicationId: fromTop.applicationId,
                        applicationStatus: "",
                        applicationSource: "",
                        applicationCreatedTs: fromTop.applicationCreatedTs,
                        applicationUpdatedTs: new Date().toISOString(),
                        isActive: true,
                        job_id: 0,
                        hiring_manager_id: 0,
                        hiringManagerReason: "",
                        applicationRankStatus: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$screenResumeConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["APPLICATION_UPDATE_STATUS"].DEMOTED,
                        atsScore: fromTop.atsScore,
                        aiReason: fromTop.aiReason,
                        aiDecision: fromTop.aiDecision
                    };
                    setCandidates((prev)=>[
                            ...prev,
                            demoted
                        ]);
                }
            } else {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t(response?.data?.message));
            }
        } catch  {} finally{
            setActiveDropdown(null);
            setDisable(false);
            setFullScreenLoading(false);
        }
    };
    const fetchMoreCandidatesApplications = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CandidatesList.useCallback[fetchMoreCandidatesApplications]": async (currentOffset = 0, reset = false, searchStr = "")=>{
            if (!userData?.orgId) return;
            setLoader(true);
            try {
                const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$CandidatesServices$2f$candidatesApplicationServices$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchCandidatesApplications"])({
                    page: currentOffset,
                    limit: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_LIMIT"],
                    searchStr: searchStr,
                    isActive: true,
                    jobId: Number(paramsPromise.jobId)
                });
                console.log("response", response);
                if (response?.data?.success) {
                    const newCandidates = response.data.data.data;
                    console.log("newCandidates", newCandidates);
                    setCandidates({
                        "CandidatesList.useCallback[fetchMoreCandidatesApplications]": (prev)=>reset ? newCandidates : [
                                ...prev,
                                ...newCandidates
                            ]
                    }["CandidatesList.useCallback[fetchMoreCandidatesApplications]"]);
                    if (newCandidates.length < __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_LIMIT"]) {
                        setHasMore(false);
                    } else {
                        setHasMore(true);
                    }
                    setOffset(currentOffset + newCandidates.length);
                } else {
                    setHasMore(false);
                }
            } catch  {
                setHasMore(false);
            } finally{
                setLoader(false);
            }
        }
    }["CandidatesList.useCallback[fetchMoreCandidatesApplications]"], []);
    const loadMoreCandidates = ()=>{
        console.log("loadMoreCandidates called=================>");
        if (!loader && hasMore) fetchMoreCandidatesApplications(offset, false, searchStr);
    };
    // const lastElementRef = useCallback(
    //   (node: HTMLElement | null) => {
    //     if (loading) return;
    //     if (observer.current) observer.current.disconnect();
    //     observer.current = new IntersectionObserver((entries) => {
    //       if (entries[0].isIntersecting && hasMore) loadMoreCandidates();
    //     });
    //     if (node) observer.current.observe(node);
    //   },
    //   [loading, hasMore, offset]
    // );
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CandidatesList.useEffect": ()=>{
            if (userData?.id && userData?.orgId) fetchTopCandidates();
        }
    }["CandidatesList.useEffect"], [
        userData?.id,
        userData?.orgId
    ]);
    const debouncedHandleSearchInputChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2f$debounce$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        "CandidatesList.useCallback[debouncedHandleSearchInputChange]": ()=>{
            if (userData?.id && userData?.orgId) fetchMoreCandidatesApplications(0, true, searchStr);
        }
    }["CandidatesList.useCallback[debouncedHandleSearchInputChange]"], 1500), [
        userData?.id,
        userData?.orgId,
        searchStr,
        fetchMoreCandidatesApplications
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CandidatesList.useEffect": ()=>{
            if (userData?.id && userData?.orgId) debouncedHandleSearchInputChange();
            return ({
                "CandidatesList.useEffect": ()=>{
                    debouncedHandleSearchInputChange.cancel();
                }
            })["CandidatesList.useEffect"];
        }
    }["CandidatesList.useEffect"], [
        userData?.id,
        userData?.orgId,
        debouncedHandleSearchInputChange
    ]);
    const handleArchiveCandidate = (candidate)=>{
        setSelectedCandidate(candidate);
        setShowArchiveModal(true);
        setActiveDropdown(null);
    };
    const handleReviewCandidate = (candidate)=>{
        setSelectedCandidate(candidate);
        setShowReviewModal(true);
        setActiveDropdown(null);
    };
    const onCancelReviewModal = ()=>{
        setShowReviewModal(false);
        setSelectedCandidate(null);
    };
    const onSubmitArchiveReason = async (reason)=>{
        if (!selectedCandidate) return;
        try {
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$CandidatesServices$2f$candidatesApplicationStatusUpdateService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["archiveActiveApplication"])(selectedCandidate.applicationId, false, reason);
            if (selectedCandidate.isTopApplication) {
                setTopCandidates((prev)=>prev.filter((c)=>c.applicationId !== selectedCandidate.applicationId));
            } else {
                setCandidates((prev)=>prev.filter((c)=>c.applicationId !== selectedCandidate.applicationId));
            }
            setShowArchiveModal(false);
            setSelectedCandidate(null);
        } catch  {}
    };
    const handleStatusChangeSuccess = (candidate, status)=>{
        if (candidate?.isTopApplication) {
            if (status === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].REJECTED) {
                setTopCandidates((prev)=>prev.filter((c)=>c.applicationId !== candidate.applicationId));
            } else {
                setTopCandidates((prev)=>prev.map((c)=>c.applicationId === candidate.applicationId ? {
                            ...c,
                            applicationStatus: status
                        } : c));
            }
        } else {
            setCandidates((prev)=>prev.map((c)=>c.applicationId === candidate.applicationId ? {
                        ...c,
                        applicationStatus: status
                    } : c));
        }
        setShowReviewModal(false);
        setSelectedCandidate(null);
    };
    const handleCandidateClick = (candidateId)=>{
        router.push(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].JOBS.CANDIDATE_PROFILE}/${candidateId}`);
    };
    // Close dropdown when clicking outside
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CandidatesList.useEffect": ()=>{
            const handleClickOutside = {
                "CandidatesList.useEffect.handleClickOutside": (event)=>{
                    if (openDropdownId && dropdownRefs.current[openDropdownId] && !dropdownRefs.current[openDropdownId]?.contains(event.target) && !event.target.closest(".applications-sources-modal")) {
                        setOpenDropdownId(null);
                    }
                }
            }["CandidatesList.useEffect.handleClickOutside"];
            document.addEventListener("mousedown", handleClickOutside);
            return ({
                "CandidatesList.useEffect": ()=>{
                    document.removeEventListener("mousedown", handleClickOutside);
                }
            })["CandidatesList.useEffect"];
        }
    }["CandidatesList.useEffect"], [
        openDropdownId
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            loadingFullScreen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$commonComponent$2f$FullPageLoader$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                lineNumber: 331,
                columnNumber: 29
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$commonPage$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].resume_page} ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$commonPage$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].candidates_list_page}`,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "container",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "common-page-header",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "common-page-head-section",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "main-heading",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$BackArrowIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    onClick: ()=>router.back()
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                    lineNumber: 339,
                                                    columnNumber: 19
                                                }, this),
                                                t("candidates_for"),
                                                " ",
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: searchParamsPromise.title
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                    lineNumber: 340,
                                                    columnNumber: 41
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                            lineNumber: 338,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "right-action",
                                            children: hasManualResumeScreeningPermission && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                className: "primary-btn rounded-md button-sm",
                                                onClick: ()=>router.push(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].SCREEN_RESUME.MANUAL_CANDIDATE_UPLOAD}/${paramsPromise.jobId}?title=${searchParamsPromise.title}&jobUniqueId=${searchParamsPromise.jobUniqueId}`),
                                                children: t("add_candidate")
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                lineNumber: 344,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                            lineNumber: 342,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                    lineNumber: 337,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                lineNumber: 336,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                            lineNumber: 335,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$commonPage$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].candidates_list_section,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$commonPage$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].section_name,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            children: [
                                                " ",
                                                t("top_ten_candidates"),
                                                " "
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                            lineNumber: 362,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            children: [
                                                " ",
                                                t("based_on_interview_date"),
                                                " "
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                            lineNumber: 363,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                    lineNumber: 361,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: `table-responsive ${topCandidates.length < 3 ? "min-data" : ""}`,
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
                                        className: "table overflow-auto mb-0",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("thead", {
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                            style: {
                                                                width: "20%"
                                                            },
                                                            children: [
                                                                " ",
                                                                t("candidate_name"),
                                                                " "
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                            lineNumber: 369,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                            style: {
                                                                width: "20%"
                                                            },
                                                            children: [
                                                                " ",
                                                                t("date_submitted"),
                                                                " "
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                            lineNumber: 370,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                            style: {
                                                                width: "20%"
                                                            },
                                                            className: "text-center",
                                                            children: t("ats_score")
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                            lineNumber: 371,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                            style: {
                                                                width: "20%"
                                                            },
                                                            className: "text-center",
                                                            children: t("candidates_analysis")
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                            lineNumber: 375,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                            style: {
                                                                width: "20%"
                                                            },
                                                            className: "text-center",
                                                            children: [
                                                                " ",
                                                                t("actions"),
                                                                " "
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                            lineNumber: 379,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                    lineNumber: 368,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                lineNumber: 367,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
                                                children: topCandidates.length > 0 ? topCandidates.map((candidate, index)=>{
                                                    const isPromoted = candidate.applicationRankStatus === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$screenResumeConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["APPLICATION_UPDATE_STATUS"].PROMOTED;
                                                    const isDemoted = candidate.applicationRankStatus === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$screenResumeConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["APPLICATION_UPDATE_STATUS"].DEMOTED;
                                                    const dotClass = isPromoted ? "green-dot" : isDemoted ? "red-dot" : "";
                                                    const statusClass = candidate.applicationStatus === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].APPROVED ? "color-success" : candidate.applicationStatus === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].REJECTED ? "color-danger" : "color-dark";
                                                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                style: {
                                                                    width: "20%"
                                                                },
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    onClick: ()=>handleCandidateClick(candidate.candidateId),
                                                                    className: `color-primary cursor-pointer text-decoration-underline ${dotClass} d-inline`,
                                                                    children: [
                                                                        index + 1,
                                                                        ". ",
                                                                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toTitleCase"])(candidate.candidateName)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                    lineNumber: 400,
                                                                    columnNumber: 29
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                lineNumber: 399,
                                                                columnNumber: 27
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                style: {
                                                                    width: "20%"
                                                                },
                                                                children: candidate.applicationCreatedTs ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$dayjs$2e$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(candidate.applicationCreatedTs).format("MMM D, YYYY") : "Not Available"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                lineNumber: 407,
                                                                columnNumber: 27
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                style: {
                                                                    width: "20%"
                                                                },
                                                                className: "text-center",
                                                                children: candidate.atsScore
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                lineNumber: 410,
                                                                columnNumber: 27
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                style: {
                                                                    width: "20%",
                                                                    textAlign: "center"
                                                                },
                                                                className: statusClass,
                                                                children: candidate.applicationStatus
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                lineNumber: 413,
                                                                columnNumber: 27
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                style: {
                                                                    width: "20%"
                                                                },
                                                                align: "center",
                                                                className: "position-relative",
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    onClick: ()=>activeDropdown ? setActiveDropdown(null) : setActiveDropdown(candidate.candidateId),
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                            className: "clear-btn p-0",
                                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$ThreeDotsIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                                                fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                                lineNumber: 421,
                                                                                columnNumber: 33
                                                                            }, this)
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                            lineNumber: 420,
                                                                            columnNumber: 31
                                                                        }, this),
                                                                        activeDropdown === candidate.candidateId && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                                            className: "custom-dropdown",
                                                                            children: [
                                                                                candidate.applicationStatus === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].APPROVED && hasEditScheduledInterviewsPermission && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                                    onClick: ()=>router.push(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].INTERVIEW.SCHEDULE_INTERVIEW}?applicationId=${candidate.applicationId}&candidateName=${encodeURIComponent(candidate.candidateName)}&title=${encodeURIComponent(searchParamsPromise.title)}&jobId=${paramsPromise.jobId}&jobUniqueId=${encodeURIComponent(searchParamsPromise.jobUniqueId)}`),
                                                                                    children: [
                                                                                        t("schedule_interview"),
                                                                                        " "
                                                                                    ]
                                                                                }, void 0, true, {
                                                                                    fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                                    lineNumber: 426,
                                                                                    columnNumber: 37
                                                                                }, this),
                                                                                hasAddAdditionalCandidateInfoPermission && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                                    onClick: ()=>router.push(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].INTERVIEW.ADD_CANDIDATE_INFO}?applicationId=${candidate.applicationId}`),
                                                                                    children: [
                                                                                        t("add_candidates_info"),
                                                                                        " "
                                                                                    ]
                                                                                }, void 0, true, {
                                                                                    fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                                    lineNumber: 437,
                                                                                    columnNumber: 37
                                                                                }, this),
                                                                                [
                                                                                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].PENDING,
                                                                                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].ON_HOLD
                                                                                ].includes(candidate.applicationStatus) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                                    onClick: ()=>handleReviewCandidate(candidate),
                                                                                    children: t("analyze_candidate_resume")
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                                    lineNumber: 444,
                                                                                    columnNumber: 37
                                                                                }, this),
                                                                                hasManageTopCandidatesPermission && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                                    onClick: !disable ? ()=>handlePromoteDemoteCandidate(candidate, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$screenResumeConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["APPLICATION_UPDATE_STATUS"].DEMOTED) : undefined,
                                                                                    children: t("demote_candidate")
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                                    lineNumber: 447,
                                                                                    columnNumber: 37
                                                                                }, this),
                                                                                hasArchiveRestoreCandidatesPermission && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                                    onClick: ()=>handleArchiveCandidate(candidate),
                                                                                    children: t("archive_candidate")
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                                    lineNumber: 462,
                                                                                    columnNumber: 37
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                            lineNumber: 424,
                                                                            columnNumber: 33
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                    lineNumber: 419,
                                                                    columnNumber: 29
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                lineNumber: 418,
                                                                columnNumber: 27
                                                            }, this)
                                                        ]
                                                    }, candidate.candidateId, true, {
                                                        fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                        lineNumber: 398,
                                                        columnNumber: 25
                                                    }, this);
                                                }) : !loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                        colSpan: 5,
                                                        className: "text-center",
                                                        children: t(topCandidates.length ? "no_more_candidates_to_fetch" : "no_candidates_found")
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                        lineNumber: 473,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                    lineNumber: 472,
                                                    columnNumber: 21
                                                }, this) : null
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                lineNumber: 385,
                                                columnNumber: 17
                                            }, this),
                                            loading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$skeletons$2f$TableSkeleton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                rows: 3,
                                                cols: 5,
                                                colWidths: "120,80,100,24,24"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                lineNumber: 479,
                                                columnNumber: 29
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                        lineNumber: 366,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                    lineNumber: 365,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                            lineNumber: 360,
                            columnNumber: 11
                        }, this),
                        candidates.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$commonPage$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].candidates_list_section,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$commonPage$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].section_name} d-flex align-items-center justify-content-between`,
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                children: t("other_candidates")
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                lineNumber: 489,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                children: t("rancked_by_resume")
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                lineNumber: 490,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                        lineNumber: 488,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                    lineNumber: 487,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: `table-responsive ${candidates.length < 3 ? "min-data" : ""}`,
                                    id: "scrollableCandidatesTableDiv",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$infinite$2d$scroll$2d$component$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        dataLength: candidates.length,
                                        next: ()=>loadMoreCandidates(),
                                        hasMore: hasMore,
                                        height: window.innerHeight - 300,
                                        loader: loader && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
                                            className: "table w-100",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$skeletons$2f$TableSkeleton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                rows: 3,
                                                cols: 5,
                                                colWidths: "120,80,100,24,24"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                lineNumber: 520,
                                                columnNumber: 25
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                            lineNumber: 519,
                                            columnNumber: 23
                                        }, void 0),
                                        endMessage: !loader && candidates.length ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
                                            className: "table w-100",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                        colSpan: 5,
                                                        style: {
                                                            textAlign: "center",
                                                            backgroundColor: "#fff"
                                                        },
                                                        children: t("no_more_candidates_to_fetch")
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                        lineNumber: 529,
                                                        columnNumber: 29
                                                    }, void 0)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                    lineNumber: 528,
                                                    columnNumber: 27
                                                }, void 0)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                lineNumber: 527,
                                                columnNumber: 25
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                            lineNumber: 526,
                                            columnNumber: 23
                                        }, void 0) : null,
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
                                            className: "table overflow-auto mb-0",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("thead", {
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                                style: {
                                                                    width: "20%"
                                                                },
                                                                children: t("candidate_name")
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                lineNumber: 541,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                                style: {
                                                                    width: "20%"
                                                                },
                                                                children: t("date_submitted")
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                lineNumber: 542,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                                style: {
                                                                    width: "20%"
                                                                },
                                                                className: "text-center",
                                                                children: t("ats_score")
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                lineNumber: 544,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                                style: {
                                                                    width: "20%"
                                                                },
                                                                className: "text-center",
                                                                children: t("candidates_analysis")
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                lineNumber: 548,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                                style: {
                                                                    width: "20%"
                                                                },
                                                                className: "text-center",
                                                                children: t("actions")
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                lineNumber: 551,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                        lineNumber: 540,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                    lineNumber: 539,
                                                    columnNumber: 21
                                                }, this),
                                                candidates.length > 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
                                                    id: "scrollableCandidatesTableBody",
                                                    children: candidates.map((candidate, index)=>{
                                                        const statusClass = candidate.applicationStatus === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].APPROVED ? "color-success text-center" : candidate.applicationStatus === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].REJECTED ? "color-danger text-center" : "color-dark text-center";
                                                        const isPromoted = candidate.applicationRankStatus === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$screenResumeConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["APPLICATION_UPDATE_STATUS"].PROMOTED;
                                                        const isDemoted = candidate.applicationRankStatus === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$screenResumeConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["APPLICATION_UPDATE_STATUS"].DEMOTED;
                                                        const dotClass = isPromoted ? "green-dot" : isDemoted ? "red-dot" : "";
                                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        onClick: ()=>handleCandidateClick(candidate.candidateId),
                                                                        className: `color-primary cursor-pointer text-decoration-underline ${dotClass} d-inline`,
                                                                        children: [
                                                                            index + 1,
                                                                            ". ",
                                                                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toTitleCase"])(candidate.candidateName) || "Candidate Name"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                        lineNumber: 576,
                                                                        columnNumber: 33
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                    lineNumber: 575,
                                                                    columnNumber: 31
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                    children: candidate.applicationCreatedTs ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$dayjs$2e$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(candidate.applicationCreatedTs).format("MMM D, YYYY") : "Not Available"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                    lineNumber: 583,
                                                                    columnNumber: 31
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                    className: "text-center",
                                                                    children: candidate.atsScore ?? "N/A"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                    lineNumber: 586,
                                                                    columnNumber: 31
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                    className: statusClass,
                                                                    children: candidate.applicationStatus
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                    lineNumber: 587,
                                                                    columnNumber: 31
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                    align: "center",
                                                                    className: "position-relative",
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        onClick: ()=>activeDropdown ? setActiveDropdown(null) : setActiveDropdown(candidate.candidateId),
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                                className: "clear-btn p-0",
                                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$ThreeDotsIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                                                    fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                                    lineNumber: 591,
                                                                                    columnNumber: 37
                                                                                }, this)
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                                lineNumber: 590,
                                                                                columnNumber: 35
                                                                            }, this),
                                                                            activeDropdown === candidate.candidateId && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                                                className: "custom-dropdown",
                                                                                ref: (element)=>{
                                                                                    if (element) {
                                                                                        dropdownRefs.current[String(candidate.candidateId)] = element;
                                                                                    }
                                                                                },
                                                                                children: [
                                                                                    candidate.applicationStatus === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].APPROVED && hasEditScheduledInterviewsPermission && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                                        onClick: ()=>router.push(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].INTERVIEW.SCHEDULE_INTERVIEW}?applicationId=${candidate.applicationId}&candidateName=${encodeURIComponent(candidate.candidateName)}&title=${encodeURIComponent(searchParamsPromise.title)}&jobId=${paramsPromise.jobId}&jobUniqueId=${encodeURIComponent(searchParamsPromise.jobUniqueId)}`),
                                                                                        children: t("schedule_interview")
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                                        lineNumber: 603,
                                                                                        columnNumber: 41
                                                                                    }, this),
                                                                                    hasAddAdditionalCandidateInfoPermission && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                                        onClick: ()=>router.push(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].INTERVIEW.ADD_CANDIDATE_INFO}?applicationId=${candidate.applicationId}`),
                                                                                        children: t("add_candidates_info")
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                                        lineNumber: 614,
                                                                                        columnNumber: 41
                                                                                    }, this),
                                                                                    [
                                                                                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].PENDING,
                                                                                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].ON_HOLD
                                                                                    ].includes(candidate.applicationStatus) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                                        onClick: ()=>handleReviewCandidate(candidate),
                                                                                        children: t("analyze_candidate_resume")
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                                        lineNumber: 623,
                                                                                        columnNumber: 41
                                                                                    }, this),
                                                                                    candidate.applicationStatus !== __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].REJECTED && hasManageTopCandidatesPermission && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                                        onClick: !disable ? ()=>handlePromoteDemoteCandidate(candidate, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$screenResumeConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["APPLICATION_UPDATE_STATUS"].PROMOTED) : undefined,
                                                                                        style: disable ? {
                                                                                            pointerEvents: "none",
                                                                                            opacity: 0.5
                                                                                        } : {},
                                                                                        children: t("promote_candidate")
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                                        lineNumber: 627,
                                                                                        columnNumber: 41
                                                                                    }, this),
                                                                                    hasArchiveRestoreCandidatesPermission && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                                        onClick: ()=>handleArchiveCandidate(candidate),
                                                                                        children: t("archive_candidate")
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                                        lineNumber: 637,
                                                                                        columnNumber: 41
                                                                                    }, this)
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                                lineNumber: 594,
                                                                                columnNumber: 37
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                        lineNumber: 589,
                                                                        columnNumber: 33
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                                    lineNumber: 588,
                                                                    columnNumber: 31
                                                                }, this)
                                                            ]
                                                        }, candidate.candidateId, true, {
                                                            fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                            lineNumber: 571,
                                                            columnNumber: 29
                                                        }, this);
                                                    })
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                    lineNumber: 557,
                                                    columnNumber: 23
                                                }, this) : !loading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                            colSpan: 5,
                                                            style: {
                                                                textAlign: "center"
                                                            },
                                                            children: t("no_candidates_found")
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                            lineNumber: 651,
                                                            columnNumber: 29
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                        lineNumber: 650,
                                                        columnNumber: 27
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                                    lineNumber: 649,
                                                    columnNumber: 25
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                            lineNumber: 538,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                        lineNumber: 512,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                                    lineNumber: 511,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                            lineNumber: 486,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                    lineNumber: 334,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                lineNumber: 333,
                columnNumber: 7
            }, this),
            showReviewModal && selectedCandidate && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$commonModals$2f$CandidateApproveRejectModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                onClickCancel: onCancelReviewModal,
                onSuccess: handleStatusChangeSuccess,
                candidate: selectedCandidate
            }, void 0, false, {
                fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                lineNumber: 667,
                columnNumber: 9
            }, this),
            showArchiveModal && selectedCandidate && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$commonModals$2f$ArchiveCandidateModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                onClickCancel: ()=>setShowArchiveModal(false),
                applicationId: selectedCandidate.applicationId,
                jobId: Number(paramsPromise.jobId),
                onSuccess: (reason)=>onSubmitArchiveReason(reason)
            }, void 0, false, {
                fileName: "[project]/src/components/views/resume/CandidatesList.tsx",
                lineNumber: 671,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true);
};
_s(CandidatesList, "0QIMl06Strhqk8dc6ZJukFCSdnw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTranslations"]
    ];
});
_c = CandidatesList;
const __TURBOPACK__default__export__ = CandidatesList;
var _c;
__turbopack_context__.k.register(_c, "CandidatesList");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/candidates-list/[jobId]/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$resume$2f$CandidatesList$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/views/resume/CandidatesList.tsx [app-client] (ecmascript)");
"use client";
;
;
const page = ({ params, searchParams })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$resume$2f$CandidatesList$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            params: params,
            searchParams: searchParams
        }, void 0, false, {
            fileName: "[project]/src/app/candidates-list/[jobId]/page.tsx",
            lineNumber: 8,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/candidates-list/[jobId]/page.tsx",
        lineNumber: 7,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = page;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=_38b22daa._.js.map