{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/home.module.scss.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"advantage_card\": \"home-module-scss-module__7j9g1q__advantage_card\",\n  \"banner_content\": \"home-module-scss-module__7j9g1q__banner_content\",\n  \"banner_image\": \"home-module-scss-module__7j9g1q__banner_image\",\n  \"card_body\": \"home-module-scss-module__7j9g1q__card_body\",\n  \"card_image\": \"home-module-scss-module__7j9g1q__card_image\",\n  \"hero_section\": \"home-module-scss-module__7j9g1q__hero_section\",\n  \"home_page\": \"home-module-scss-module__7j9g1q__home_page\",\n  \"page_heading\": \"home-module-scss-module__7j9g1q__page_heading\",\n  \"top_brand_image\": \"home-module-scss-module__7j9g1q__top_brand_image\",\n  \"top_brand_section\": \"home-module-scss-module__7j9g1q__top_brand_section\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/logo-group.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 864, height: 175, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAYAAABllJ3tAAAATUlEQVR42gFCAL3/ABsVEyUODAkZDAcALwYDACACAAEdAQAAFAAAACEAAAAwAAcLAAwQGAAzAQEAJw0DBQ4pCQ8hEQgKEwgJCCkAAAATbrADSGnAdlEAAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 2 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,oIAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA8M,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/hirng-hero.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 823, height: 664, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAYAAAD+Bd/7AAAA0UlEQVR42gHGADn/ALq5uuG1r671dH+Um5C18vRvi7nFMTU6WHZvZuF4cV/+AI2Mja6Qjo7MXGZ5g22Px890jrrNXGJplISAdNZwaljWAGRkY7t1eX3bgYB/1nl+gt08ODnVcmQ+msOjTN3DokreAIJ+d9SXlJDcmZKN2np8ft1KSUq9OzYkUKiMQLvFpU3dAH5qNY+4m0zRenRdmnqUwcxQZYaYHiEmNHh2ca6cmI7WAN64UPbLqErhREExWG2PxsWPserwUFpqp4CJj/2Di5H/gP1p6tpwhIIAAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 6 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,oIAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA8X,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/upload-resume-screening.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 3088, height: 1900, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAYAAAB4ka1VAAAAaUlEQVR42iWNSRLFIAhEvf8l/7hOpVQQUNMBsuhqhgddzOwSUZhNqGuujTndo/d5GWOg1obeCTw0l60Rfq832GdFVcHMCDBq/4jjJHw/f7TaHRABEUPcA3pgSY/o/BBAXGa2a62V2nvjBhv5m5iMT4SMAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 5 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,oJAAA,CAAA,UAAG;IAAE,OAAO;IAAM,QAAQ;IAAM,aAAa;IAAkP,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/scored-results-img.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 3088, height: 2056, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAYAAAB4ka1VAAAAg0lEQVR42j3KSwrCMBSF4ex/C66jGxA6sAMLThxICFUwoCaxzctLHsc2gw4+Dgd+ZvQne7eglIxa67oFOee222fLrPE1Cs5Z+BDhI4GIWtSC83XCcOHoRwFxGyB5D4q2BRt2OHboxhMmaTCrO4LmSOSREzVMqwfUS8AauXrufu6NFBT+Gv6XUm4TaU0AAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 5 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,+IAAA,CAAA,UAAG;IAAE,OAAO;IAAM,QAAQ;IAAM,aAAa;IAAsR,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 125, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/performance-based-image.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 767, height: 492, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAYAAAB4ka1VAAAAsElEQVR42gGlAFr/AKSintJ5b2PodnFq6JSSjc9ya2KkdG5kozQ0MkkAAAAAAHR2eKNJQzu0T01Ju56cm+B/al/ijYB24UJCQWUAAAAAABERERJOTktWfn15hqimorWXjoemX1tXbTg3NTwNDQ0MAIKAeoLP0Mvs397X/Onm3v7p5Nv+4t7V9dXSyeWBf3qGACUlIyVgXltjd3Rwfo2KhZajoJmwu7ixzIeFgJUlJCMnBdZOQv9gbEAAAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 5 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,oJAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAAkV,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 149, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/dashboard-admin-img.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 1544, height: 1038, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAYAAAB4ka1VAAAAc0lEQVR42k1OyQ4CIRTj/3/Qi0eNMxOEt7A+AxU52UvTpJsjYlNVlFLQ7YM5J/7hHpcgclqGilo7WjeYGVrrmx1JBUlBlIznESDppzNohUQLHKcGHwSvi3C7Hzi9bn2dHv7NcJoNLIoQedePMdZUXTz3ny/ZnJpUl5pezAAAAABJRU5ErkJggg==\", blurWidth: 8, blurHeight: 5 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,gJAAA,CAAA,UAAG;IAAE,OAAO;IAAM,QAAQ;IAAM,aAAa;IAAkQ,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/app/home/<USER>"], "sourcesContent": ["\"use client\";\nimport Button from \"@/components/formElements/Button\";\nimport styles from \"@/styles/home.module.scss\";\nimport Image from \"next/image\";\nimport brands from \"../../../public/assets/images/logo-group.png\";\nimport HiringHero from \"../../../public/assets/images/hirng-hero.png\";\nimport uploadResumeImg from \"../../../public/assets/images/upload-resume-screening.png\";\nimport scoredResultsImg from \"../../../public/assets/images/scored-results-img.png\";\nimport performanceBasedHiringImg from \"../../../public/assets/images/performance-based-image.png\";\nimport DashboardImg from \"../../../public/assets/images/dashboard-admin-img.png\";\nconst HomePage = () => {\n  return (\n    <div className={styles.home_page}>\n      <section className={`${styles.hero_section}`}>\n        <div className=\"container\">\n          <div className=\"row align-items-center g-5\">\n            <div className=\"col-md-6\">\n              <div className={`${styles.banner_content}`}>\n                <h1>\n                  Smarter Hiring with <span>AI Intelligence</span>\n                </h1>\n                <p>\n                  Say goodbye to gut-based hiring and resume overload. Stratum 9’s Hiring Manager Module brings science, structure, and insight to the\n                  heart of recruitment—so you can hire top performers, not just top profiles.\n                </p>\n                <Button className=\"dark-btn rounded-md\">Get Started</Button>\n              </div>\n            </div>\n            <div className=\"col-md-6\">\n              <div className={`${styles.banner_image}`}>\n                <Image src={HiringHero} alt=\"main banner\" width={640} height={320} />\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n      <section className={styles.top_brand_section}>\n        <div className=\"container\">\n          <div className=\"row\">\n            <div className=\"col-md-5\">\n              <div className={`${styles.page_heading}`}>\n                <h2>\n                  Top Brands Partnered with <span>Stratum 9 Hiring</span>\n                </h2>\n                <p>We are trusted by top brands globally for their hiring needs.</p>\n              </div>\n            </div>\n            <div className=\"offset-md-1 col-md-6\">\n              <div className={`${styles.top_brand_image}`}>\n                <Image src={brands} alt=\"top brands\" />\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n      <section className={styles.advantage_section}>\n        <div className=\"container\">\n          <div className=\"row g-5\">\n            <div className=\"col-md-8 mb-5\">\n              <div className={styles.page_heading}>\n                <h2>\n                  What Powers the <span>Stratum 9 Advantage</span>\n                </h2>\n                <p>\n                  Designed for Precision. Built for Performance. Discover the features that turn traditional hiring into a smart, structured, and\n                  scalable process. Powered by AI, guided by psychology, and tailored for real-world results.\n                </p>\n              </div>\n            </div>\n            <div className=\"col-md-6\">\n              <div className={styles.advantage_card}>\n                <div className={styles.card_body}>\n                  <h3>Structured Interviews. Scored Results</h3>\n                  <p>\n                    Interview guides generated around key performance skills. Score candidates based on what matters—drive, grit, leadership,\n                    coachability, and decision-making.\n                  </p>\n                </div>\n                <div className={styles.card_image}>\n                  <Image src={scoredResultsImg} alt=\"Scored Results\" />\n                </div>\n              </div>\n            </div>\n            <div className=\"col-md-6\">\n              <div className={styles.advantage_card}>\n                <div className={styles.card_body}>\n                  <h3>Performance-Based Hiring Philosophy</h3>\n                  <p>\n                    Go beyond credentials. Our module evaluates candidates based on behavioral performance, adaptability, emotional intelligence, and\n                    core competencies aligned to real-world success.\n                  </p>\n                </div>\n                <div className={styles.card_image}>\n                  <Image src={performanceBasedHiringImg} alt=\"Performance Based Hiring\" />\n                </div>\n              </div>\n            </div>\n            <div className=\"col-md-6\">\n              <div className={styles.advantage_card}>\n                <div className={styles.card_body}>\n                  <h3>AI-Powered Screening & Interviews</h3>\n                  <p>\n                    Instantly generate job descriptions, match resumes using AI, and conduct structured interviews with preloaded performance\n                    questions tailored to your organization’s culture and values.\n                  </p>\n                </div>\n                <div className={styles.card_image}>\n                  <Image src={uploadResumeImg} alt=\"AI Screening\" />\n                </div>\n              </div>\n            </div>\n            <div className=\"col-md-6\">\n              <div className={styles.advantage_card}>\n                <div className={styles.card_body}>\n                  <h3>Data-Driven Hiring Decisions</h3>\n                  <p>\n                    View real-time dashboards, top 10 candidate rankings, source tracking, interview summaries, and final assessments with AI-backed\n                    scoring metrics.\n                  </p>\n                </div>\n                <div className={styles.card_image}>\n                  <Image src={DashboardImg} alt=\"Performance Based Hiring\" />\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default HomePage;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;;AAUA,MAAM,WAAW;IACf,qBACE,8OAAC;QAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,SAAS;;0BAC9B,8OAAC;gBAAQ,WAAW,GAAG,mJAAA,CAAA,UAAM,CAAC,YAAY,EAAE;0BAC1C,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAW,GAAG,mJAAA,CAAA,UAAM,CAAC,cAAc,EAAE;;sDACxC,8OAAC;;gDAAG;8DACkB,8OAAC;8DAAK;;;;;;;;;;;;sDAE5B,8OAAC;sDAAE;;;;;;sDAIH,8OAAC,4IAAA,CAAA,UAAM;4CAAC,WAAU;sDAAsB;;;;;;;;;;;;;;;;;0CAG5C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAW,GAAG,mJAAA,CAAA,UAAM,CAAC,YAAY,EAAE;8CACtC,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCAAC,KAAK,sTAAA,CAAA,UAAU;wCAAE,KAAI;wCAAc,OAAO;wCAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMxE,8OAAC;gBAAQ,WAAW,mJAAA,CAAA,UAAM,CAAC,iBAAiB;0BAC1C,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAW,GAAG,mJAAA,CAAA,UAAM,CAAC,YAAY,EAAE;;sDACtC,8OAAC;;gDAAG;8DACwB,8OAAC;8DAAK;;;;;;;;;;;;sDAElC,8OAAC;sDAAE;;;;;;;;;;;;;;;;;0CAGP,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAW,GAAG,mJAAA,CAAA,UAAM,CAAC,eAAe,EAAE;8CACzC,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCAAC,KAAK,sTAAA,CAAA,UAAM;wCAAE,KAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMlC,8OAAC;gBAAQ,WAAW,mJAAA,CAAA,UAAM,CAAC,iBAAiB;0BAC1C,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,YAAY;;sDACjC,8OAAC;;gDAAG;8DACc,8OAAC;8DAAK;;;;;;;;;;;;sDAExB,8OAAC;sDAAE;;;;;;;;;;;;;;;;;0CAMP,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,cAAc;;sDACnC,8OAAC;4CAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,SAAS;;8DAC9B,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAE;;;;;;;;;;;;sDAKL,8OAAC;4CAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,UAAU;sDAC/B,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDAAC,KAAK,4UAAA,CAAA,UAAgB;gDAAE,KAAI;;;;;;;;;;;;;;;;;;;;;;0CAIxC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,cAAc;;sDACnC,8OAAC;4CAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,SAAS;;8DAC9B,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAE;;;;;;;;;;;;sDAKL,8OAAC;4CAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,UAAU;sDAC/B,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDAAC,KAAK,sVAAA,CAAA,UAAyB;gDAAE,KAAI;;;;;;;;;;;;;;;;;;;;;;0CAIjD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,cAAc;;sDACnC,8OAAC;4CAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,SAAS;;8DAC9B,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAE;;;;;;;;;;;;sDAKL,8OAAC;4CAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,UAAU;sDAC/B,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDAAC,KAAK,sVAAA,CAAA,UAAe;gDAAE,KAAI;;;;;;;;;;;;;;;;;;;;;;0CAIvC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,cAAc;;sDACnC,8OAAC;4CAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,SAAS;;8DAC9B,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAE;;;;;;;;;;;;sDAKL,8OAAC;4CAAI,WAAW,mJAAA,CAAA,UAAM,CAAC,UAAU;sDAC/B,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDAAC,KAAK,8UAAA,CAAA,UAAY;gDAAE,KAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShD;uCAEe", "debugId": null}}, {"offset": {"line": 660, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}